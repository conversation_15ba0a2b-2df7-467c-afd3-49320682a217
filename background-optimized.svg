<svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid slice">
    <g clip-path="url(#clip0_optimized)">
        <!-- 基础渐变背景 -->
        <rect width="1440" height="900" fill="url(#paint0_linear_optimized)"/>
        
        <!-- 简化的光晕效果 - 减少数量和复杂度 -->
        <g opacity="0.4">
            <circle cx="977.5" cy="638.5" r="400" fill="#3234CA" filter="url(#blur_simple)"/>
        </g>
        <g opacity="0.3">
            <ellipse cx="300" cy="600" rx="350" ry="300" fill="#9B38C6" filter="url(#blur_simple)"/>
        </g>
        <g opacity="0.25">
            <ellipse cx="1200" cy="200" rx="300" ry="250" fill="#668CFF" filter="url(#blur_light)"/>
        </g>
    </g>
    <defs>
        <!-- 单一简化的模糊滤镜 -->
        <filter id="blur_simple" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="80" result="effect1_foregroundBlur"/>
        </filter>
        
        <!-- 更轻的模糊滤镜 -->
        <filter id="blur_light" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur"/>
        </filter>
        
        <!-- 背景渐变 -->
        <linearGradient id="paint0_linear_optimized" x1="116.5" y1="39" x2="693.5" y2="590.5" gradientUnits="userSpaceOnUse">
            <stop stop-color="#0C1325"/>
            <stop offset="1" stop-color="#090616"/>
        </linearGradient>
        
        <clipPath id="clip0_optimized">
            <rect width="1440" height="900" fill="white"/>
        </clipPath>
    </defs>
</svg>