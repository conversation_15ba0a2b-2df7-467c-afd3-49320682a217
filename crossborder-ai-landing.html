<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-lang-key="page.title">网红营销智能体 - AI驱动的网红建联管家</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.staticfile.net">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Outfit:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="dns-prefetch" href="//demo.leadcoins.club">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-bg: #0f0f1e;
            --card-bg: rgba(26, 27, 46, 0.8);
            --accent-purple: #7c3aed;
            --accent-blue: #4f46e5;
            --text-primary: #ffffff;
            --text-secondary: #a5a6b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* SVG 背景效果 - 性能优化版 */
        .svg-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
            will-change: transform;
            transform: translateZ(0); /* 开启硬件加速 */
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            perspective: 1000px;
        }

        .svg-bg svg {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: translate3d(0, 0, 0); /* 强制GPU渲染 */
        }

        /* 导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            z-index: 50;
            padding: 1rem 0;
            background: rgba(15, 15, 30, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(15, 15, 30, 0.98);
        }

        /* 按钮样式 */
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            padding: 12px 28px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(124, 58, 237, 0.4);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* Hero Section 动画 */
        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
            line-height: 1.2;
            background: linear-gradient(to right, #ffffff, #a5a6b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: fadeInUp 1s ease-out;
        }

        /* 标题字体优化 */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
        }

        .font-bold, .font-semibold {
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
        }

        /* 性能优化：动画延迟类 */
        .animate-delay-1 { animation-delay: 0.1s; }
        .animate-delay-2 { animation-delay: 0.2s; }
        .animate-delay-3 { animation-delay: 0.3s; }
        .animate-delay-4 { animation-delay: 0.4s; }
        .animate-delay-5 { animation-delay: 0.5s; }

        /* 性能优化：减少重绘重排 */
        .scroll-reveal {
            will-change: transform, opacity;
            transform: translateZ(0);
        }

        .glass-card {
            will-change: transform;
            transform: translateZ(0);
        }

        /* 性能优化：减少重绘重排 */
        .navbar {
            transform: translateZ(0);
            will-change: background-color, padding;
        }

        .floating-cta {
            will-change: opacity, transform;
        }

        /* 性能优化：GPU加速的动画 */
        .animate-bounce {
            transform: translateZ(0);
        }

        .stat-number {
            will-change: contents;
        }

        /* 性能优化：避免layout thrashing */
        .solution-card, .feature-icon, .pricing-card {
            transform: translateZ(0);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 卡片样式 */
        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            transform: translateY(-5px);
            border-color: rgba(124, 58, 237, 0.5);
            box-shadow: 0 20px 40px rgba(124, 58, 237, 0.2);
        }

        /* 功能图标 */
        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .feature-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            opacity: 0.2;
            z-index: -1;
        }

        /* 数据展示动画 */
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 工作流程线条 */
        .workflow-line {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 30px;
            height: 2px;
            background: linear-gradient(to right, transparent, var(--accent-purple), transparent);
            z-index: -1;
        }

        /* 价格卡片 */
        .pricing-card {
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            border: 2px solid var(--accent-purple);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: '最受欢迎';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--accent-purple);
            color: white;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 12px;
            font-weight: 600;
        }

        /* 联系我们部分样式优化 */
        .contact-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .contact-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }

            .glass-card {
                padding: 1.5rem;
            }

            .workflow-line {
                display: none;
            }

            /* 联系我们移动端优化 */
            .contact-card {
                padding: 1.5rem;
            }
        }

        /* 滚动动画 - 优化快速滑动体验 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(20px); /* 减少偏移量 */
            transition: opacity 0.3s ease, transform 0.3s ease; /* 减少过渡时间 */
        }

        .scroll-reveal.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        /* 防止内容跳动 - 针对不同元素设置合适的最小高度 */
        .scroll-reveal:not(.active) {
            min-height: 80px; /* 默认最小高度 */
        }
        
        .glass-card.scroll-reveal:not(.active) {
            min-height: 200px; /* 卡片元素需要更高 */
        }
        
        .text-center.scroll-reveal:not(.active) {
            min-height: 60px; /* 文字区域可以矮一些 */
        }

        /* AI 动画效果 */
        .ai-glow {
            position: relative;
        }

        .ai-glow::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--primary-gradient);
            border-radius: inherit;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            filter: blur(10px);
        }

        .ai-glow:hover::after {
            opacity: 0.5;
        }

        /* 悬浮按钮 */
        .floating-cta {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 999;
            display: none;
        }

        .floating-cta.show {
            display: block;
            animation: slideInRight 0.5s ease;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 解决方案卡片样式 */
        .solution-card {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            overflow: hidden;
            height: 451px;
            box-shadow: 0px 12px 32px 0px rgba(6, 1, 43, 0.1);
            transition: all 0.3s ease;
        }

        .solution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0px 20px 40px 0px rgba(6, 1, 43, 0.2);
        }

        .solution-card-header {
            height: 112px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 32px;
            border-radius: 12px 12px 0 0;
        }

        .problem-header {
            background: rgba(255, 255, 255, 0.05);
        }

        .ai-header {
            background: linear-gradient(to right, rgba(114, 112, 255, 0.8), rgba(243, 112, 255, 0.5));
        }

        .solution-card-content {
            padding: 12px 0;
            height: calc(451px - 112px);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        .solution-item {
            display: flex;
            align-items: center;
            padding: 24px 40px;
            gap: 12px;
            height: 74px;
        }

        .solution-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .problem-icon {
            background: rgba(208, 71, 71, 0.2);
            color: #d04747;
        }

        .ai-icon {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .solution-text {
            font-size: 18px;
            font-family: 'Inter', 'Noto Sans SC', 'PingFang SC', sans-serif;
            font-weight: 400;
            line-height: normal;
            margin: 0;
            white-space: nowrap;
        }

        .problem-card .solution-text {
            color: rgba(255, 255, 255, 0.6);
        }

        .ai-text {
            color: rgba(255, 255, 255, 0.9);
        }

        /* 响应式处理 */
        @media (max-width: 1024px) {
            .solution-card {
                height: auto;
                min-height: 400px;
            }
            
            .solution-item {
                padding: 20px 24px;
                height: auto;
                min-height: 60px;
            }
            
            .solution-text {
                white-space: normal;
                font-size: 16px;
            }
        }

        @media (max-width: 768px) {
            .solution-card-header {
                height: 80px;
                padding: 0 20px;
            }
            
            .solution-card-header h3 {
                font-size: 20px;
            }
            
            .solution-item {
                padding: 16px 20px;
            }
            
            .solution-text {
                font-size: 14px;
            }
            
            .solution-icon {
                width: 28px;
                height: 28px;
            }
        }
    </style>
</head>
<body>
    <!-- SVG 背景 - 性能优化版 -->
    <div class="svg-bg">
        <svg width="1440" height="900" viewBox="0 0 1440 900" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid slice">
            <g clip-path="url(#clip0_optimized)">
                <!-- 基础渐变背景 -->
                <rect width="1440" height="900" fill="url(#paint0_linear_optimized)"/>
                
                <!-- 简化的光晕效果 - 减少数量和复杂度 -->
                <g opacity="0.4">
                    <circle cx="977.5" cy="638.5" r="400" fill="#3234CA" filter="url(#blur_simple)"/>
                </g>
                <g opacity="0.3">
                    <ellipse cx="300" cy="600" rx="350" ry="300" fill="#9B38C6" filter="url(#blur_simple)"/>
                </g>
                <g opacity="0.25">
                    <ellipse cx="1200" cy="200" rx="300" ry="250" fill="#668CFF" filter="url(#blur_light)"/>
                </g>
            </g>
            <defs>
                <!-- 单一简化的模糊滤镜 -->
                <filter id="blur_simple" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="80" result="effect1_foregroundBlur"/>
                </filter>
                
                <!-- 更轻的模糊滤镜 -->
                <filter id="blur_light" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="60" result="effect1_foregroundBlur"/>
                </filter>
                
                <!-- 背景渐变 -->
                <linearGradient id="paint0_linear_optimized" x1="116.5" y1="39" x2="693.5" y2="590.5" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#0C1325"/>
                    <stop offset="1" stop-color="#090616"/>
                </linearGradient>
                
                <clipPath id="clip0_optimized">
                    <rect width="1440" height="900" fill="white"/>
                </clipPath>
            </defs>
        </svg>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar py-4 relative z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 text-white" aria-label="Brand Logo">
                    <span class="sr-only" data-lang-key="brand.name">网红营销智能体</span>
                    <div class="w-[219px] h-8 flex items-center">
                        <svg viewBox="0 0 219 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-full">
                          <g clip-path="url(#clip0_2941_5200)">
                            <path d="M17.8271 26.999H11.0957V16.9023H17.8271V26.999ZM1 16.9023V5.36426H14.4619L1 16.9023ZM27.9229 16.9023L14.4619 5.36426H27.9229V16.9023Z" fill="currentColor"/>
                            <path d="M38.1035 21.7017L40.3936 18.6616H42.6191V26.9995H40.3564V21.6099L38.0312 24.6743L35.7314 21.6108V26.9995H33.5811V18.6616H35.8691L38.1035 21.7017ZM52.2236 26.9995H49.8984L49.3604 25.3618H46.377L45.8477 26.9995H43.748L46.8604 18.6616H49.3486L52.2236 26.9995ZM57.5498 18.6616C58.5164 18.6616 59.2796 18.8821 59.8379 19.3237C60.3962 19.7654 60.6748 20.4408 60.6748 21.3491C60.6748 21.6906 60.6044 22.0364 60.4629 22.3862C60.3296 22.7362 60.1212 23.0453 59.8379 23.312C59.6109 23.5252 59.3321 23.6801 59.0029 23.7798L61.125 26.9995H58.2256L56.6758 24.2368H55.6123V26.9995H53.3506V18.6616H57.5498ZM64.8203 21.8853L67.0703 18.6616H69.9199L66.8662 22.103L70.1572 26.9995H67.3447L65.3115 23.856L64.8203 24.4097V26.9995H62.5449V18.6616H64.8203V21.8853ZM77.1621 20.3862H73.624V21.9614H76.6748V23.687H73.6123V25.2739H77.1621V26.9995H71.3496V18.6616H77.1621V20.3862ZM85.0957 20.3862H82.833V26.9995H80.583V20.3862H78.3584V18.6616H85.0957V20.3862ZM46.8975 23.7495H48.8301L47.8555 20.7827L46.8975 23.7495ZM55.6123 22.6118H57.2256C57.5087 22.6117 57.7459 22.524 57.9375 22.3491C58.1292 22.1741 58.2256 21.8781 58.2256 21.4614C58.2255 21.0949 58.1335 20.8239 57.9502 20.6489C57.7753 20.474 57.5502 20.3863 57.2754 20.3862H55.6123V22.6118Z" fill="currentColor"/>
                            <path d="M43.4053 10.3623C43.4053 10.9537 43.5174 11.3915 43.7422 11.6748C43.9755 11.9581 44.3549 12.1006 44.8799 12.1006C45.3466 12.1006 45.7094 11.9581 45.9678 11.6748C46.2259 11.3915 46.3545 10.9537 46.3545 10.3623V5.2002H48.5049V10.4873C48.5049 11.2623 48.3547 11.896 48.0547 12.3877C47.7547 12.8793 47.338 13.2423 46.8047 13.4756C46.2798 13.7088 45.6754 13.8252 44.9922 13.8252C43.6674 13.8252 42.6759 13.5586 42.0176 13.0254C41.3676 12.4921 41.042 11.6456 41.042 10.4873V5.2002H43.4053V10.3623ZM76.5547 5C77.0963 5 77.6216 5.05378 78.1299 5.16211C78.6465 5.27044 79.1422 5.42066 79.6172 5.6123V7.65039C79.0922 7.36706 78.613 7.14564 78.1797 6.9873C77.7547 6.82064 77.3128 6.7373 76.8545 6.7373C76.4212 6.73732 76.0461 6.81631 75.7295 6.97461C75.4212 7.12461 75.1842 7.39206 75.0176 7.77539C74.851 8.15034 74.7676 8.67916 74.7676 9.3623C74.7676 10.029 74.8543 10.5585 75.0293 10.9502C75.2043 11.3335 75.4378 11.6044 75.7295 11.7627C76.0295 11.921 76.3629 12 76.7295 12C76.9378 12 77.1254 11.9879 77.292 11.9629C77.4669 11.9296 77.6129 11.8835 77.7295 11.8252V10.5371H76.4795V8.8125H79.9922V12.7627C79.459 13.071 78.9006 13.308 78.3174 13.4746C77.7425 13.6329 77.159 13.7129 76.5674 13.7129C75.7507 13.7129 75.0337 13.5704 74.417 13.2871C73.8004 13.0038 73.3167 12.5454 72.9668 11.9121C72.6253 11.2705 72.4551 10.4248 72.4551 9.375C72.4551 8.35007 72.6252 7.51665 72.9668 6.875C73.3168 6.22506 73.8004 5.7502 74.417 5.4502C75.0337 5.15019 75.7463 5 76.5547 5ZM39.7686 6.9248H37.5059V13.5381H35.2559V6.9248H33.0312V5.2002H39.7686V6.9248ZM54.5352 5.2002C55.5016 5.2002 56.264 5.42085 56.8223 5.8623C57.3806 6.30397 57.6602 6.97936 57.6602 7.8877C57.6602 8.22929 57.5889 8.57488 57.4473 8.9248C57.3139 9.27481 57.1056 9.58392 56.8223 9.85059C56.5952 10.0638 56.3166 10.2187 55.9873 10.3184L58.1104 13.5381H55.21L53.6602 10.7754H52.5977V13.5381H50.335V5.2002H54.5352ZM61.8047 13.5381H59.5303V5.2002H61.8047V13.5381ZM68.9219 9.72559V5.2002H70.9971V13.5381H69.3467L65.6973 8.84766V13.5381H63.6348V5.2002H65.5098L68.9219 9.72559ZM52.5977 9.15039H54.21C54.4933 9.15039 54.7312 9.0627 54.9229 8.8877C55.1144 8.71268 55.21 8.41657 55.21 8C55.2099 7.63352 55.1188 7.36247 54.9355 7.1875C54.7605 7.0125 54.5348 6.9248 54.2598 6.9248H52.5977V9.15039Z" fill="currentColor"/>
                            <path d="M99 6V26" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                            <path d="M125.076 11.742C125.316 12.234 125.622 12.72 125.994 13.2C126.366 13.68 126.774 14.124 127.218 14.532C127.674 14.928 128.13 15.258 128.586 15.522C128.478 15.618 128.346 15.744 128.19 15.9C128.046 16.044 127.908 16.2 127.776 16.368C127.656 16.524 127.548 16.668 127.452 16.8C126.984 16.464 126.516 16.056 126.048 15.576C125.58 15.096 125.148 14.568 124.752 13.992C124.356 13.416 124.02 12.834 123.744 12.246L125.076 11.742ZM113.772 11.004V13.794H116.454V11.004H113.772ZM112.368 9.546H117.912V15.252H112.368V9.546ZM114.834 14.622H116.328V22.776H114.834V14.622ZM112.44 16.782H113.754V23.316H112.44V16.782ZM111.63 23.046C112.206 22.914 112.872 22.758 113.628 22.578C114.384 22.386 115.182 22.176 116.022 21.948C116.862 21.72 117.702 21.498 118.542 21.282L118.758 22.74C117.582 23.088 116.4 23.43 115.212 23.766C114.024 24.102 112.962 24.402 112.026 24.666L111.63 23.046ZM115.464 17.502H118.038V18.996H115.464V17.502ZM118.488 17.268H128.064V18.708H118.488V17.268ZM118.65 10.986H128.154V12.462H118.65V10.986ZM120.648 14.622H125.418V16.008H120.648V14.622ZM120.882 20.256H126.066V21.66H120.396L120.882 20.256ZM120.594 18.258H122.178C122.022 18.846 121.854 19.452 121.674 20.076C121.494 20.688 121.32 21.216 121.152 21.66H119.55C119.73 21.192 119.91 20.652 120.09 20.04C120.282 19.416 120.45 18.822 120.594 18.258ZM125.616 20.256H127.182C127.182 20.256 127.176 20.34 127.164 20.508C127.152 20.664 127.14 20.796 127.128 20.904C127.056 21.732 126.972 22.422 126.876 22.974C126.792 23.526 126.69 23.964 126.57 24.288C126.45 24.612 126.306 24.858 126.138 25.026C125.934 25.218 125.7 25.35 125.436 25.422C125.184 25.494 124.872 25.542 124.5 25.566C124.212 25.578 123.792 25.578 123.24 25.566C122.688 25.566 122.106 25.548 121.494 25.512C121.482 25.284 121.416 25.032 121.296 24.756C121.188 24.492 121.05 24.258 120.882 24.054C121.314 24.09 121.734 24.114 122.142 24.126C122.562 24.15 122.934 24.162 123.258 24.162C123.594 24.174 123.846 24.18 124.014 24.18C124.218 24.18 124.38 24.168 124.5 24.144C124.632 24.132 124.746 24.096 124.842 24.036C125.034 23.88 125.184 23.532 125.292 22.992C125.412 22.44 125.514 21.594 125.598 20.454L125.616 20.256ZM122.592 9.024L124.104 9.402C123.588 10.974 122.862 12.384 121.926 13.632C120.99 14.868 119.892 15.888 118.632 16.692C118.596 16.56 118.524 16.398 118.416 16.206C118.32 16.014 118.218 15.822 118.11 15.63C118.002 15.426 117.906 15.264 117.822 15.144C118.938 14.496 119.91 13.65 120.738 12.606C121.566 11.562 122.184 10.368 122.592 9.024ZM136.146 10.032H145.29V11.418H136.146V10.032ZM135.606 13.308H145.758V14.748H135.606V13.308ZM137.514 11.814L138.9 11.526C139.02 11.79 139.14 12.084 139.26 12.408C139.38 12.72 139.464 12.996 139.512 13.236L138.072 13.596C138.024 13.356 137.946 13.068 137.838 12.732C137.73 12.396 137.622 12.09 137.514 11.814ZM142.374 11.562L143.94 11.868C143.76 12.24 143.586 12.606 143.418 12.966C143.25 13.314 143.088 13.614 142.932 13.866L141.618 13.56C141.75 13.272 141.888 12.936 142.032 12.552C142.188 12.168 142.302 11.838 142.374 11.562ZM137.964 18.69V19.698H143.202V18.69H137.964ZM137.964 16.656V17.646H143.202V16.656H137.964ZM136.416 15.522H144.84V20.832H136.416V15.522ZM138.162 20.436H139.764C139.704 21.12 139.59 21.738 139.422 22.29C139.266 22.83 139.02 23.31 138.684 23.73C138.36 24.138 137.91 24.492 137.334 24.792C136.758 25.104 136.032 25.362 135.156 25.566C135.108 25.434 135.036 25.284 134.94 25.116C134.844 24.96 134.736 24.798 134.616 24.63C134.496 24.474 134.382 24.348 134.274 24.252C135.042 24.108 135.666 23.928 136.146 23.712C136.638 23.484 137.022 23.22 137.298 22.92C137.574 22.608 137.772 22.248 137.892 21.84C138.024 21.432 138.114 20.964 138.162 20.436ZM141.204 20.454H142.788V23.388C142.788 23.652 142.83 23.808 142.914 23.856C142.998 23.916 143.136 23.946 143.328 23.946C143.412 23.946 143.52 23.946 143.652 23.946C143.796 23.946 143.922 23.946 144.03 23.946C144.138 23.946 144.234 23.94 144.318 23.928C144.414 23.916 144.492 23.904 144.552 23.892C144.672 23.844 144.762 23.718 144.822 23.514C144.846 23.394 144.858 23.208 144.858 22.956C144.87 22.704 144.882 22.404 144.894 22.056C145.038 22.188 145.242 22.32 145.506 22.452C145.77 22.584 146.01 22.686 146.226 22.758C146.214 23.118 146.184 23.46 146.136 23.784C146.1 24.108 146.052 24.342 145.992 24.486C145.836 24.798 145.596 25.008 145.272 25.116C145.14 25.164 144.966 25.2 144.75 25.224C144.534 25.248 144.342 25.26 144.174 25.26C144.078 25.26 143.952 25.26 143.796 25.26C143.64 25.26 143.484 25.26 143.328 25.26C143.184 25.26 143.064 25.26 142.968 25.26C142.752 25.26 142.518 25.23 142.266 25.17C142.026 25.122 141.828 25.032 141.672 24.9C141.504 24.78 141.384 24.606 141.312 24.378C141.24 24.162 141.204 23.802 141.204 23.298V20.454ZM139.494 8.988L141.078 8.682C141.222 8.946 141.36 9.246 141.492 9.582C141.624 9.906 141.72 10.188 141.78 10.428L140.124 10.824C140.064 10.584 139.974 10.29 139.854 9.942C139.734 9.594 139.614 9.276 139.494 8.988ZM129.81 13.164H135.012V14.766H129.81V13.164ZM131.7 9.024H133.356V21.102H131.7V9.024ZM129.522 21.498C130.002 21.354 130.548 21.18 131.16 20.976C131.772 20.76 132.42 20.526 133.104 20.274C133.788 20.022 134.472 19.77 135.156 19.518L135.498 21.084C134.562 21.456 133.62 21.834 132.672 22.218C131.736 22.59 130.872 22.932 130.08 23.244L129.522 21.498ZM153.84 9.834H162.984V11.436H153.84V9.834ZM152.634 13.902H164.226V15.486H152.634V13.902ZM159.312 16.782L160.734 16.134C161.094 16.71 161.472 17.328 161.868 17.988C162.276 18.648 162.66 19.29 163.02 19.914C163.392 20.538 163.692 21.078 163.92 21.534L162.39 22.326C162.186 21.858 161.91 21.306 161.562 20.67C161.214 20.022 160.842 19.362 160.446 18.69C160.05 18.006 159.672 17.37 159.312 16.782ZM151.716 15.036V22.488H150.06V16.62H147.684V15.036H151.716ZM148.116 10.716L149.25 9.636C149.586 9.864 149.952 10.122 150.348 10.41C150.756 10.698 151.146 10.98 151.518 11.256C151.902 11.52 152.214 11.754 152.454 11.958L151.284 13.2C151.056 12.984 150.756 12.732 150.384 12.444C150.024 12.144 149.64 11.844 149.232 11.544C148.836 11.244 148.464 10.968 148.116 10.716ZM151.086 21.804C151.362 21.804 151.638 21.906 151.914 22.11C152.19 22.302 152.58 22.548 153.084 22.848C153.684 23.22 154.374 23.46 155.154 23.568C155.946 23.664 156.84 23.712 157.836 23.712C158.328 23.712 158.862 23.706 159.438 23.694C160.014 23.67 160.602 23.64 161.202 23.604C161.814 23.568 162.402 23.526 162.966 23.478C163.53 23.43 164.046 23.376 164.514 23.316C164.454 23.484 164.382 23.682 164.298 23.91C164.214 24.138 164.142 24.366 164.082 24.594C164.034 24.822 164.004 25.02 163.992 25.188C163.668 25.212 163.254 25.236 162.75 25.26C162.258 25.284 161.718 25.302 161.13 25.314C160.554 25.326 159.972 25.338 159.384 25.35C158.796 25.362 158.262 25.368 157.782 25.368C156.654 25.368 155.7 25.296 154.92 25.152C154.14 25.02 153.432 24.774 152.796 24.414C152.436 24.198 152.1 23.976 151.788 23.748C151.488 23.52 151.242 23.406 151.05 23.406C150.858 23.406 150.636 23.514 150.384 23.73C150.132 23.946 149.868 24.222 149.592 24.558C149.316 24.894 149.034 25.254 148.746 25.638L147.576 24.018C147.984 23.586 148.392 23.208 148.8 22.884C149.22 22.548 149.622 22.284 150.006 22.092C150.402 21.9 150.762 21.804 151.086 21.804ZM153.804 21.912C153.768 21.78 153.714 21.612 153.642 21.408C153.582 21.204 153.51 20.994 153.426 20.778C153.342 20.55 153.264 20.358 153.192 20.202C153.372 20.166 153.54 20.058 153.696 19.878C153.852 19.698 154.02 19.476 154.2 19.212C154.296 19.08 154.428 18.864 154.596 18.564C154.764 18.264 154.956 17.91 155.172 17.502C155.388 17.082 155.598 16.638 155.802 16.17C156.006 15.702 156.186 15.222 156.342 14.73L158.232 15.27C157.944 15.954 157.608 16.644 157.224 17.34C156.852 18.036 156.462 18.696 156.054 19.32C155.658 19.932 155.262 20.478 154.866 20.958V20.994C154.866 20.994 154.812 21.024 154.704 21.084C154.596 21.144 154.47 21.228 154.326 21.336C154.194 21.432 154.074 21.534 153.966 21.642C153.858 21.738 153.804 21.828 153.804 21.912ZM153.804 21.912L153.75 20.562L154.74 19.968L162.408 19.392C162.444 19.62 162.498 19.878 162.57 20.166C162.642 20.454 162.702 20.688 162.75 20.868C161.274 21.012 160.032 21.132 159.024 21.228C158.016 21.324 157.2 21.402 156.576 21.462C155.952 21.522 155.46 21.576 155.1 21.624C154.74 21.672 154.464 21.72 154.272 21.768C154.08 21.804 153.924 21.852 153.804 21.912ZM170.904 16.728V18.114H177.168V16.728H170.904ZM169.302 15.558H178.86V19.284H169.302V15.558ZM167.934 20.22H180.336V25.53H178.644V21.696H169.572V25.548H167.934V20.22ZM168.834 23.532H179.418V24.936H168.834V23.532ZM166.53 13.272H181.632V16.872H179.976V14.604H168.096V16.872H166.53V13.272ZM166.062 10.194H181.974V11.724H166.062V10.194ZM169.86 8.808H171.534V12.822H169.86V8.808ZM176.394 8.808H178.086V12.822H176.394V8.808ZM191.424 12.804H198.876V14.406H191.424V12.804ZM198.156 12.804H199.794C199.794 12.804 199.794 12.858 199.794 12.966C199.794 13.062 199.788 13.176 199.776 13.308C199.776 13.428 199.776 13.524 199.776 13.596C199.74 15.48 199.698 17.076 199.65 18.384C199.602 19.692 199.542 20.766 199.47 21.606C199.41 22.434 199.326 23.082 199.218 23.55C199.122 24.018 199.002 24.348 198.858 24.54C198.666 24.816 198.462 25.008 198.246 25.116C198.03 25.224 197.766 25.302 197.454 25.35C197.178 25.386 196.812 25.398 196.356 25.386C195.912 25.386 195.456 25.38 194.988 25.368C194.976 25.128 194.922 24.846 194.826 24.522C194.742 24.21 194.628 23.946 194.484 23.73C194.964 23.766 195.408 23.79 195.816 23.802C196.224 23.814 196.518 23.82 196.698 23.82C196.854 23.82 196.986 23.802 197.094 23.766C197.202 23.718 197.298 23.64 197.382 23.532C197.502 23.388 197.598 23.106 197.67 22.686C197.754 22.254 197.826 21.636 197.886 20.832C197.946 20.028 197.994 18.996 198.03 17.736C198.078 16.476 198.12 14.952 198.156 13.164V12.804ZM194.16 8.808H195.816C195.816 10.248 195.798 11.646 195.762 13.002C195.726 14.346 195.63 15.63 195.474 16.854C195.33 18.078 195.084 19.224 194.736 20.292C194.388 21.36 193.896 22.338 193.26 23.226C192.636 24.114 191.832 24.882 190.848 25.53C190.728 25.326 190.548 25.098 190.308 24.846C190.068 24.606 189.846 24.408 189.642 24.252C190.566 23.664 191.316 22.968 191.892 22.164C192.48 21.36 192.93 20.466 193.242 19.482C193.554 18.498 193.77 17.442 193.89 16.314C194.022 15.186 194.1 13.992 194.124 12.732C194.148 11.46 194.16 10.152 194.16 8.808ZM184.818 9.618H190.884V21.354H189.282V11.166H186.348V22.506H184.818V9.618ZM183.54 22.002C184.224 21.87 185.01 21.72 185.898 21.552C186.798 21.372 187.752 21.174 188.76 20.958C189.768 20.742 190.77 20.526 191.766 20.31L191.928 21.84C190.98 22.068 190.02 22.296 189.048 22.524C188.088 22.752 187.164 22.968 186.276 23.172C185.388 23.376 184.578 23.568 183.846 23.748L183.54 22.002ZM185.61 13.452H190.056V14.964H185.61V13.452ZM185.61 17.25H190.056V18.744H185.61V17.25ZM215.238 8.808L216.498 10.194C215.634 10.434 214.668 10.65 213.6 10.842C212.544 11.022 211.434 11.178 210.27 11.31C209.118 11.43 207.954 11.532 206.778 11.616C205.614 11.688 204.486 11.748 203.394 11.796C203.358 11.568 203.298 11.31 203.214 11.022C203.13 10.722 203.046 10.482 202.962 10.302C204.042 10.254 205.152 10.188 206.292 10.104C207.432 10.02 208.548 9.918 209.64 9.798C210.744 9.666 211.776 9.522 212.736 9.366C213.696 9.198 214.53 9.012 215.238 8.808ZM203.052 13.902H217.164V15.522H203.052V13.902ZM201.828 18.114H218.208V19.77H201.828V18.114ZM209.136 10.986H210.918V23.334C210.918 23.886 210.834 24.294 210.666 24.558C210.51 24.822 210.24 25.026 209.856 25.17C209.472 25.302 208.962 25.386 208.326 25.422C207.702 25.458 206.922 25.47 205.986 25.458C205.95 25.302 205.89 25.122 205.806 24.918C205.734 24.714 205.65 24.51 205.554 24.306C205.458 24.102 205.362 23.928 205.266 23.784C205.758 23.796 206.226 23.808 206.67 23.82C207.126 23.82 207.522 23.82 207.858 23.82C208.206 23.808 208.446 23.802 208.578 23.802C208.782 23.802 208.926 23.766 209.01 23.694C209.094 23.622 209.136 23.49 209.136 23.298V10.986Z" fill="currentColor"/>
                          </g>
                          <defs>
                            <clipPath id="clip0_2941_5200">
                              <rect width="219" height="32" fill="currentColor"/>
                            </clipPath>
                          </defs>
                        </svg>
                    </div>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#solution" class="text-gray-300 hover:text-white transition" data-lang-key="nav.features">产品功能</a>
                    <a href="#features" class="text-gray-300 hover:text-white transition" data-lang-key="nav.solutions">解决方案</a>
                    <a href="#pricing" class="text-gray-300 hover:text-white transition" data-lang-key="nav.pricing">价格</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 语言切换 -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center space-x-2 text-gray-300 hover:text-white transition">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">中文</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="languageDropdown" class="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg hidden min-w-[140px] z-50">
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="zh">
                                <i class="fas fa-flag mr-2 text-red-400"></i>
                                <span>中文</span>
                            </button>
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="en">
                                <i class="fas fa-flag mr-2 text-blue-400"></i>
                                <span>English</span>
                            </button>
                        </div>
                    </div>
                    
                    <button class="btn-primary" data-lang-key="cta.start" onclick="handleButtonClick()">
                        <i class="fab fa-google"></i>
                        申请免费试用
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative py-20 overflow-hidden">
        <div class="container mx-auto px-4 relative z-10">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="hero-title mb-6">
                                            <span data-lang-key="hero.title">一站式的AI网红营销平台</span><br>
                    <span class="text-3xl md:text-4xl" data-lang-key="hero.subtitle">一键分析商品、匹配创作者、自动跟进</span>
                </h1>
                
                <p class="text-xl text-gray-300 mb-8" data-lang-key="hero.description">
                    减少 <span class="text-purple-400 font-bold">75%</span> 筛选时间，
                    提升 <span class="text-purple-400 font-bold">5x</span> 回复率，
                    让 AI 接管您的网红营销全流程
                </p>
                
                <div class="flex justify-center mb-12">
                    <button class="btn-primary text-lg px-8 py-4" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                        <i class="fas fa-magic"></i>
                        申请免费试用
                    </button>
                </div>
                
                <!-- 动态演示区域 -->
                <div class="glass-card max-w-3xl mx-auto p-8 ai-glow">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-red-500"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <span class="text-sm text-gray-400">AI Assistant</span>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <i class="fas fa-link text-purple-400 mt-1"></i>
                            <div class="flex-1">
                                <p class="text-sm text-gray-400 mb-3" data-lang-key="demo.instruction">粘贴商品链接，AI立即开始分析</p>
                                <div class="flex gap-2">
                                    <input type="url" 
                                           placeholder="https://example.com/product..." 
                                           class="flex-1 px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none transition"
                                           id="productUrlInput"
                                           data-lang-key="demo.placeholder">
                                    <button class="btn-primary px-6 py-3 text-sm" onclick="handleButtonClick()" data-lang-key="demo.analyze">
                                        <i class="fas fa-magic mr-1"></i>
                                        分析
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="analysisLoader" class="flex items-center justify-center py-4" style="display: none;">
                            <div class="flex space-x-2">
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce"></div>
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce animate-delay-1"></div>
                                <div class="w-2 h-2 rounded-full bg-purple-400 animate-bounce animate-delay-2"></div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <i class="fas fa-users text-3xl text-green-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result1">匹配 <span class="font-bold">千万+</span> 位博主</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-envelope text-3xl text-blue-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result2">生成个性化邮件</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-chart-line text-3xl text-purple-400 mb-2"></i>
                                <p class="text-sm" data-lang-key="demo.result3">预测 ROI <span class="font-bold">180%</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 痛点与解决方案 -->
    <section class="py-20 relative" id="solution">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="solution.title">
                    告别传统建联的痛苦
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="solution.subtitle">
                    我们用 AI 解决您的每一个难题
                </p>
            </div>
            
            <!-- 左右对比卡片 -->
            <div class="max-w-7xl mx-auto mb-20">
                <div class="grid lg:grid-cols-2 gap-10 items-start">
                    <!-- 传统方式困扰卡片 -->
                    <div class="solution-card problem-card scroll-reveal">
                        <div class="solution-card-header problem-header">
                            <h3 class="text-2xl font-semibold text-white" data-lang-key="solution.problems.title">
                                传统方式的困扰
                            </h3>
                        </div>
                        <div class="solution-card-content">
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item1">人工查找博主费时费力，效率极低</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item2">邮件模板千篇一律，回复率惨淡</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item3">沟通进度分散，难以追踪管理</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon problem-icon">
                                    <i class="fas fa-times"></i>
                                </div>
                                <p class="solution-text" data-lang-key="solution.problems.item4">效果难以衡量，ROI 不透明</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI 解决方案卡片 -->
                    <div class="solution-card ai-card scroll-reveal animate-delay-2">
                        <div class="solution-card-header ai-header">
                            <h3 class="text-2xl font-semibold text-white" data-lang-key="solution.ai.title">
                                AI 驱动的解决方案
                            </h3>
                        </div>
                        <div class="solution-card-content">
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item1">AI 秒级检索 千万+ 博主库，智能过滤</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-magic"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item2">大模型生成个性化邮件，自动 A/B 迭代</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item3">全流程看板 + 漏斗视图，阶段可视化</p>
                            </div>
                            <div class="solution-item">
                                <div class="solution-icon ai-icon">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <p class="solution-text ai-text" data-lang-key="solution.ai.item4">实时 KPI 仪表盘，ROI 一目了然</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据对比 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                <div class="text-center scroll-reveal">
                    <div class="stat-number">120%</div>
                    <p class="text-gray-400" data-lang-key="stats.engagement">互动增长</p>
                </div>
                <div class="text-center scroll-reveal animate-delay-1">
                    <div class="stat-number">75%</div>
                    <p class="text-gray-400" data-lang-key="stats.timeSaved">时间节省</p>
                </div>
                <div class="text-center scroll-reveal animate-delay-2">
                    <div class="stat-number">5x</div>
                    <p class="text-gray-400" data-lang-key="stats.responseRate">回复率提升</p>
                </div>
                <div class="text-center scroll-reveal animate-delay-3">
                    <div class="stat-number">85%</div>
                    <p class="text-gray-400" data-lang-key="stats.accuracy">匹配精准度</p>
                </div>
            </div>
        </div>
    </section>

    <!-- AI 驱动的智能分析 -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="analysis.title">
                    AI 驱动的智能分析
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="analysis.subtitle">
                    深度洞察，让每一次建联都精准有效
                </p>
            </div>
            
            <div class="glass-card max-w-6xl mx-auto p-8">
                <div class="grid md:grid-cols-2 gap-8">
                    <!-- 左侧：概念关系图 -->
                    <div class="relative h-96 flex items-center justify-center">
                        <div class="relative w-80 h-80">
                            <!-- 中心节点 - 绝对居中 -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold shadow-2xl z-10" data-lang-key="analysis.core">
                                AI 核心
                            </div>
                            
                            <!-- 周围节点 - 相对于中心精确定位，统一紫色系 -->
                            <!-- 左上 -->
                            <div class="absolute top-4 left-4 w-24 h-24 rounded-full bg-purple-600/20 border-2 border-purple-600 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node1">
                                商品分析
                            </div>
                            <!-- 右上 -->
                            <div class="absolute top-4 right-4 w-24 h-24 rounded-full bg-purple-500/20 border-2 border-purple-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node2">
                                博主匹配
                            </div>
                            <!-- 左下 -->
                            <div class="absolute bottom-4 left-4 w-24 h-24 rounded-full bg-indigo-500/20 border-2 border-indigo-500 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node3">
                                邮件优化
                            </div>
                            <!-- 右下 -->
                            <div class="absolute bottom-4 right-4 w-24 h-24 rounded-full bg-purple-400/20 border-2 border-purple-400 flex items-center justify-center text-sm text-center px-2" data-lang-key="analysis.node4">
                                效果追踪
                            </div>
                            
                            <!-- 连接线 - 从中心到四个角 -->
                            <svg class="absolute inset-0 w-full h-full" style="z-index: 1;">
                                <!-- 左上线 -->
                                <line x1="50%" y1="50%" x2="20%" y2="20%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 右上线 -->
                                <line x1="50%" y1="50%" x2="80%" y2="20%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 左下线 -->
                                <line x1="50%" y1="50%" x2="20%" y2="80%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                                <!-- 右下线 -->
                                <line x1="50%" y1="50%" x2="80%" y2="80%" stroke="rgba(147, 51, 234, 0.4)" stroke-width="2" stroke-dasharray="4,4">
                                    <animate attributeName="stroke-dashoffset" values="0;8" dur="2s" repeatCount="indefinite"/>
                                </line>
                            </svg>
                            

                        </div>
                    </div>
                    
                    <!-- 右侧：关键指标 -->
                    <div class="space-y-6">
                        <h3 class="text-2xl font-semibold mb-4" data-lang-key="analysis.metrics.title">实时性能指标</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.accuracy">博主匹配精准度</span>
                                    <span class="text-purple-300 font-bold">85%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-purple-400 to-pink-400 h-4 rounded-full shadow-lg shadow-purple-500/30 transition-all duration-300" style="width: 85%"></div>
                                </div>
                            </div>
                            
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.openRate">邮件打开率</span>
                                    <span class="text-indigo-300 font-bold">42%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-indigo-400 to-purple-400 h-4 rounded-full shadow-lg shadow-indigo-500/30 transition-all duration-300" style="width: 42%"></div>
                                </div>
                            </div>

                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.conversionRate">回复转化率</span>
                                    <span class="text-purple-300 font-bold">28%</span>
                                </div>
                                <div class="w-full bg-gray-800/60 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-purple-500 to-purple-400 h-4 rounded-full shadow-lg shadow-purple-500/30 transition-all duration-300" style="width: 28%"></div>
                                </div>
                            </div>

                            <div>
                                <div class="flex justify-between mb-2">
                                    <span data-lang-key="analysis.metrics.roi">ROI 表现</span>
                                    <span class="text-purple-300 font-bold">180%</span>
                                </div>
                                <div class="w-full rounded-full h-4 shadow-inner relative overflow-hidden" style="background-color: rgba(31, 41, 55, 0.6);">
                                    <div class="h-4 rounded-full shadow-lg transition-all duration-300 relative" style="width: 100%; background: linear-gradient(to right, #a855f7, #c084fc); box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);">
                                        <div class="absolute inset-0 rounded-full animate-pulse" style="background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 p-4 bg-purple-500/10 rounded-lg border border-purple-500/30">
                            <p class="text-sm" data-lang-key="analysis.metrics.note">
                                <i class="fas fa-lightbulb text-purple-400 mr-2"></i>
                                AI 持续学习优化，每次建联都比上次更精准
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 核心功能 -->
    <section class="py-20 relative" id="features">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="features.title">
                    强大功能，智能高效
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="features.subtitle">
                    六大核心模块，覆盖建联全流程
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                <!-- 功能1: AI商品解析 -->
                <div class="glass-card scroll-reveal">
                    <div class="feature-icon bg-gradient-to-br from-purple-500 to-purple-600">
                        <i class="fas fa-barcode text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature1.title">AI 商品解析</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature1.description">
                        粘贴商品链接，自动抓取标题、图片、规格，秒懂您的产品
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-clock mr-2"></i>
                        <span data-lang-key="feature1.tag">≤30秒完成解析</span>
                    </div>
                </div>

                <!-- 功能2: 智能博主匹配 -->
                <div class="glass-card scroll-reveal animate-delay-1">
                    <div class="feature-icon bg-gradient-to-br from-purple-400 to-purple-500">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature2.title">智能博主匹配</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature2.description">
                        相似度算法 + 受众画像筛选，精准找到最合适的创作者
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-bullseye mr-2"></i>
                        <span data-lang-key="feature2.tag">92% 匹配精准度</span>
                    </div>
                </div>

                                        <!-- 功能3: 一键批量建联 -->
                <div class="glass-card scroll-reveal animate-delay-2">
                    <div class="feature-icon bg-gradient-to-br from-purple-600 to-purple-700">
                        <i class="fas fa-paper-plane text-white"></i>
                    </div>
                                                <h3 class="text-xl font-semibold mb-3" data-lang-key="feature3.title">一键批量建联</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature3.description">
                        邮件生成、批量发送、意向跟进，全自动化处理
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-rocket mr-2"></i>
                        <span data-lang-key="feature3.tag">45%+ 平均回复率</span>
                    </div>
                </div>
                
                <!-- 功能4: 效果实时追踪 -->
                <div class="glass-card scroll-reveal animate-delay-3">
                    <div class="feature-icon bg-gradient-to-br from-purple-500 to-pink-500">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature4.title">效果实时追踪</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature4.description">
                        实时监控网红回信状态，智能分析合作意向强度，建立效果追踪体系
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-chart-bar mr-2"></i>
                        <span data-lang-key="feature4.tag">数据实时更新</span>
                    </div>
                </div>

                <!-- 功能5: 仪表盘 -->
                <div class="glass-card scroll-reveal animate-delay-4">
                    <div class="feature-icon bg-gradient-to-br from-indigo-500 to-purple-500">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature5.title">全流程仪表盘</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature5.description">
                        活跃合作、回复率、转化漏斗，数据驱动决策
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-eye mr-2"></i>
                        <span data-lang-key="feature5.tag">实时数据更新</span>
                    </div>
                </div>

                <!-- 功能6: 智能优化 -->
                <div class="glass-card scroll-reveal animate-delay-5">
                    <div class="feature-icon bg-gradient-to-br from-purple-400 to-pink-400">
                        <i class="fas fa-brain text-white"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-lang-key="feature6.title">AI 智能优化</h3>
                    <p class="text-gray-300 mb-4" data-lang-key="feature6.description">
                        机器学习持续优化邮件模板，动态调整发送策略
                    </p>
                    <div class="flex items-center text-purple-400 text-sm">
                        <i class="fas fa-rocket mr-2"></i>
                        <span data-lang-key="feature6.tag">持续自动优化</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 工作流程 -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="workflow.title">
                    简单五步，开启智能建联
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="workflow.subtitle">
                    从注册到成功合作，AI 全程陪伴
                </p>
            </div>
            
            <div class="relative max-w-5xl mx-auto">
                <div class="workflow-line hidden md:block"></div>
                
                <div class="grid md:grid-cols-5 gap-6">
                    <!-- 步骤1 -->
                    <div class="text-center scroll-reveal">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-400 to-purple-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            1
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step1.title">创建账户</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step1.description">快速注册开始</p>
                    </div>

                    <!-- 步骤2 -->
                    <div class="text-center scroll-reveal animate-delay-1">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            2
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step2.title">粘贴商品</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step2.description">链接或上传商品</p>
                    </div>

                    <!-- 步骤3 -->
                    <div class="text-center scroll-reveal animate-delay-2">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-600 to-purple-700 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            3
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step3.title">AI 匹配</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step3.description">智能推荐博主</p>
                    </div>

                    <!-- 步骤4 -->
                    <div class="text-center scroll-reveal animate-delay-3">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            4
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step4.title">发送邮件</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step4.description">个性化触达</p>
                    </div>

                    <!-- 步骤5 -->
                    <div class="text-center scroll-reveal animate-delay-4">
                        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center text-white font-bold text-xl relative z-10">
                            5
                        </div>
                        <h4 class="font-semibold mb-2" data-lang-key="workflow.step5.title">追踪优化</h4>
                        <p class="text-sm text-gray-400" data-lang-key="workflow.step5.description">效果实时监控</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <button class="btn-primary text-lg" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                    <i class="fas fa-rocket mr-2"></i>
                    申请免费试用
                </button>
            </div>
        </div>
    </section>

    <!-- 社会证明 -->
    <section class="py-20 relative bg-gradient-to-b from-transparent to-purple-900/10">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="social.title">
                    深受品牌信赖
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="social.subtitle">
                    已帮助 <span class="text-purple-400 font-bold">500+</span> 品牌成功出海
                </p>
            </div>
            
            <!-- 客户Logo -->
            <div class="flex flex-wrap justify-center items-center gap-8 mb-16">
                <div class="w-36 h-20 bg-gradient-to-br from-purple-500/10 to-pink-500/10 border border-purple-500/20 rounded-xl flex items-center justify-center hover:border-purple-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-purple-300 font-semibold">CreativeFlow</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border border-blue-500/20 rounded-xl flex items-center justify-center hover:border-blue-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-blue-300 font-semibold">InfluenceHub</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-green-500/10 to-teal-500/10 border border-green-500/20 rounded-xl flex items-center justify-center hover:border-green-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-green-300 font-semibold">TrendMaker</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-xl flex items-center justify-center hover:border-orange-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-orange-300 font-semibold">SocialPeak</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-pink-500/10 to-purple-500/10 border border-pink-500/20 rounded-xl flex items-center justify-center hover:border-pink-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-pink-300 font-semibold">ViralCraft</span>
                </div>
                <div class="w-36 h-20 bg-gradient-to-br from-indigo-500/10 to-purple-500/10 border border-indigo-500/20 rounded-xl flex items-center justify-center hover:border-indigo-500/40 transition-all duration-300 hover:transform hover:scale-105">
                    <span class="text-indigo-300 font-semibold">BrandBoost</span>
                </div>
            </div>
            
            <!-- 客户案例 -->
            <div class="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <div class="glass-card scroll-reveal">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-user-tie text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user1.name">张经理</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user1.title">跨境电商创始人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user1.quote">
                        "首次使用就带来了 <span class="text-purple-400 font-bold">6位数</span> 的销售额！
                        AI 匹配的博主质量远超预期。"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-1">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user2.name">李总监</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user2.title">品牌营销负责人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user2.quote">
                        "团队效率提升 <span class="text-purple-400 font-bold">200%</span>，
                        再也不用熬夜找博主了！"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-2">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-green-500 to-teal-500 mr-3 flex items-center justify-center">
                            <i class="fas fa-crown text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold" data-lang-key="testimonials.user3.name">王CEO</h4>
                            <p class="text-sm text-gray-400" data-lang-key="testimonials.user3.title">DTC品牌创始人</p>
                        </div>
                    </div>
                    <p class="text-gray-300 italic" data-lang-key="testimonials.user3.quote">
                        "ROI 达到 <span class="text-purple-400 font-bold">180%</span>，
                        这是我们做过最成功的营销投资！"
                    </p>
                    <div class="flex mt-4 text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 价格方案 -->
    <section class="py-20 relative" id="pricing">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="pricing.title">
                    透明定价，按需选择
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="pricing.subtitle">
                    零前期成本，只为结果付费
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-6 max-w-5xl mx-auto">
                <!-- Starter -->
                <div class="glass-card pricing-card scroll-reveal flex flex-col">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.starter.name">Starter</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.starter.description">适合个人和小团队</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.starter.price">
                        免费
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature1">月度 50 封建联</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature2">基础博主匹配</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature3">单人席位</span>
                        </li>
                        <li class="flex items-center text-gray-500">
                            <i class="fas fa-times text-gray-600 mr-2"></i>
                            <span data-lang-key="pricing.starter.feature4">高级数据分析</span>
                        </li>
                    </ul>
                    <button class="w-full py-3 border border-gray-600 rounded-full hover:border-purple-500 transition text-center" data-lang-key="pricing.starter.button" onclick="handleButtonClick()">
                        开始使用
                    </button>
                </div>
                
                <!-- Pro -->
                <div class="glass-card pricing-card featured scroll-reveal flex flex-col animate-delay-1">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.pro.name">Pro</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.pro.description">团队小规模投放</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.pro.price">
                        敬请期待
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature1">月度 1000 封建联</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature2">高级筛选功能</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature3">5 人团队协作</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.pro.feature4">实时数据分析</span>
                        </li>
                    </ul>
                    <button class="btn-primary w-full py-3 justify-center" data-lang-key="pricing.pro.button" onclick="handleButtonClick()">
                        立即升级
                    </button>
                </div>
                
                <!-- Enterprise -->
                <div class="glass-card pricing-card scroll-reveal flex flex-col animate-delay-2">
                    <h3 class="text-2xl font-bold mb-2" data-lang-key="pricing.enterprise.name">Enterprise</h3>
                    <p class="text-gray-400 mb-4" data-lang-key="pricing.enterprise.description">代理商和大品牌</p>
                    <div class="text-4xl font-bold mb-4" data-lang-key="pricing.enterprise.price">
                        定制
                    </div>
                    <ul class="space-y-3 mb-6 flex-grow">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature1">不限建联数量</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature2">不限团队席位</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature3">API 接入</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-400 mr-2"></i>
                            <span data-lang-key="pricing.enterprise.feature4">专属客户经理</span>
                        </li>
                    </ul>
                    <button class="w-full py-3 border border-gray-600 rounded-full hover:border-purple-500 transition text-center" data-lang-key="pricing.enterprise.button" onclick="handleButtonClick()">
                        联系销售
                    </button>
                </div>
            </div>
            
            <div class="text-center mt-8">
                <p class="text-purple-400" data-lang-key="pricing.note">
                    <i class="fas fa-info-circle mr-2"></i>
                    仅在成功合作时抽佣 5%，无隐藏费用
                </p>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12 scroll-reveal">
                <h2 class="text-3xl md:text-4xl font-bold mb-4" data-lang-key="faq.title">
                    常见问题
                </h2>
                <p class="text-xl text-gray-300" data-lang-key="faq.subtitle">
                    我们为您解答所有疑问
                </p>
            </div>
            
            <div class="max-w-3xl mx-auto space-y-4">
                <div class="glass-card scroll-reveal">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q1.question">Google 登录安全吗？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q1.answer">
                            我们使用 OAuth 2.0 标准认证，不会存储您的密码。所有数据传输均采用 SSL 加密，
                            符合 GDPR 和 ISO-27001 安全标准。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-1">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q2.question">数据源从哪里来？多久更新？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q2.answer">
                            我们整合了 YouTube、Instagram、TikTok 等主流平台的公开数据，
                            拥有 千万+ 活跃博主库，每周更新，确保数据新鲜度。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-2">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q3.question">如何保证博主真实有效？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q3.answer">
                            AI 会分析博主的互动率、粉丝增长曲线、内容质量等多维度数据，
                            自动过滤虚假账号，确保匹配的都是高质量创作者。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-3">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q4.question">是否支持 TikTok / Instagram？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q4.answer">
                            未来计划支持！我们目前专注于 YouTube 平台，
                            未来将逐步接入 TikTok、Instagram、Twitter 等更多平台，满足您的全渠道营销需求。
                        </p>
                    </details>
                </div>
                
                <div class="glass-card scroll-reveal animate-delay-4">
                    <details class="cursor-pointer">
                        <summary class="flex items-center justify-between font-semibold text-lg">
                            <span data-lang-key="faq.q5.question">AI 匹配效果如何保证？</span>
                            <i class="fas fa-chevron-down"></i>
                        </summary>
                        <p class="mt-4 text-gray-300" data-lang-key="faq.q5.answer">
                            AI 会持续学习和优化匹配算法，结合历史合作数据、受众重合度、
                            内容风格匹配等多个维度，确保推荐精准度不断提升。
                        </p>
                    </details>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="glass-card max-w-4xl mx-auto text-center p-12 ai-glow">
                <h2 class="text-3xl md:text-4xl font-bold mb-6" data-lang-key="final.cta.title">
                    准备好革新您的网红营销了吗？
                </h2>
                <p class="text-xl text-gray-300 mb-8" data-lang-key="final.cta.description">
                    加入 500+ 成功品牌，让 AI 为您的出海之路保驾护航
                </p>
                <div class="flex justify-center">
                    <button class="btn-primary text-lg px-8 py-4" data-lang-key="cta.startNow" onclick="handleButtonClick()">
                        <i class="fab fa-google mr-2"></i>
                        申请免费试用
                    </button>
                </div>
                <p class="mt-6 text-sm text-gray-400" data-lang-key="final.cta.note">
                    <i class="fas fa-shield-alt mr-2"></i>
                    无需信用卡 · 免费试用 · 随时取消
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Footer -->
    <section class="py-20 relative">
        <div class="container mx-auto px-4">
            <div class="glass-card max-w-4xl mx-auto text-center p-8 md:p-12 ai-glow mb-16 scroll-reveal">
                <!-- 标题区域 -->
                <div class="mb-12">
                    <h3 class="text-3xl md:text-4xl font-bold mb-3 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                        联系我们
                    </h3>
                    <p class="text-gray-400 text-base mb-2">Contact Us</p>
                    <p class="text-gray-300 text-sm max-w-md mx-auto">
                        我们期待与您合作，共同开启AI驱动的网红营销新时代
                    </p>
                </div>

                <!-- 联系方式垂直布局 -->
                <div class="space-y-8 mb-12">
                    <!-- 邮箱联系 -->
                    <div class="contact-card rounded-2xl p-8 scroll-reveal max-w-lg mx-auto">
                        <div class="flex flex-col items-center text-center space-y-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center">
                                <i class="fas fa-envelope text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-400 mb-2 font-medium">Business Email</p>
                                <a href="mailto:<EMAIL>"
                                   class="text-gray-200 hover:text-blue-400 text-lg font-semibold transition-colors duration-300 block">
                                    <EMAIL>
                                </a>
                                <p class="text-xs text-gray-500 mt-2">商务合作 · 产品咨询 · 技术支持</p>
                            </div>
                        </div>
                    </div>

                    <!-- 微信联系 -->
                    <div class="contact-card rounded-2xl p-8 scroll-reveal max-w-lg mx-auto">
                        <div class="flex flex-col items-center text-center space-y-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center">
                                <i class="fab fa-weixin text-white text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-400 mb-4 font-medium">WeChat Contact</p>
                                <div class="bg-white rounded-xl p-3 inline-block">
                                    <img src="wechat.png"
                                         alt="WeChat QR Code"
                                         class="w-28 h-28 rounded-lg"
                                         onerror="this.style.display='none'">
                                </div>
                                <p class="text-xs text-gray-500 mt-3">扫码添加微信联系</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速联系按钮 -->
                <div class="mb-8">
                    <button onclick="handleButtonClick()"
                            class="btn-primary text-lg px-8 py-4 shadow-2xl hover:shadow-purple-500/25">
                        <i class="fas fa-rocket mr-2"></i>
                        立即开始免费试用
                    </button>
                </div>

                <!-- 分隔线 -->
                <div class="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent my-8"></div>

                <!-- 版权信息 -->
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-2">
                        © 2025 Turing Market. All rights reserved.
                    </p>
                    <p class="text-xs text-gray-600">
                        Powered by AI · Built for Global Success
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 悬浮CTA按钮 -->
    <div class="floating-cta">
        <button class="btn-primary shadow-2xl" data-lang-key="cta.start" onclick="handleButtonClick()">
            <i class="fas fa-rocket mr-2"></i>
            申请免费试用
        </button>
    </div>

    <script>
        // 统一跳转地址
        const REDIRECT_URL = 'https://agent.turingmarket.ai/';

        // 统一按钮处理函数 - 在当前页面跳转
        function handleButtonClick() {
            window.location.href = REDIRECT_URL;
        }

        // 多语言数据
        const languages = {
            zh: {
                // 页面基本信息
                'page.title': '网红营销智能体 - AI驱动的网红建联管家',
                'brand.name': '网红营销智能体',
                
                // 导航
                'nav.features': '产品功能',
                'nav.solutions': '解决方案',
                'nav.pricing': '价格',
                'cta.start': '申请免费试用',
                'cta.startNow': '申请免费试用',
                
                // Hero 部分
                'hero.title': '一站式的AI网红营销平台',
                'hero.subtitle': '一键分析商品、匹配创作者、自动跟进',
                'hero.description': '减少 <span class="text-purple-400 font-bold">75%</span> 筛选时间，提升 <span class="text-purple-400 font-bold">5x</span> 回复率，让 AI 接管您的网红营销全流程',
                
                // 解决方案
                'solution.title': '告别传统建联的痛苦',
                'solution.subtitle': '我们用 AI 解决您的每一个难题',
                
                // 功能
                'features.title': '强大功能，智能高效',
                'features.subtitle': '六大核心模块，覆盖建联全流程',
                'feature1.title': 'AI 商品解析',
                'feature1.description': '粘贴商品链接，自动抓取标题、图片、规格，秒懂您的产品',
                'feature2.title': '智能博主匹配',
                'feature2.description': '相似度算法 + 受众画像筛选，精准找到最合适的创作者',
                'feature3.title': '一键批量建联',
                'feature3.description': '邮件生成、批量发送、意向跟进，全自动化处理',
                'feature1.tag': '≤30秒完成解析',
                'feature2.tag': '92% 匹配精准度',
                'feature3.tag': '45%+ 平均回复率',
                
                // 价格
                'pricing.title': '透明定价，按需选择',
                'pricing.subtitle': '零前期成本，只为结果付费',
                'pricing.starter.name': 'Starter',
                'pricing.starter.description': '适合个人和小团队',
                'pricing.starter.price': '免费',
                'pricing.pro.name': 'Pro',
                'pricing.pro.description': '团队小规模投放',
                'pricing.pro.price': '敬请期待',
                'pricing.enterprise.name': 'Enterprise',
                'pricing.enterprise.description': '代理商和大品牌',
                'pricing.enterprise.price': '定制',
                'pricing.starter.button': '开始使用',
                'pricing.pro.button': '立即升级',
                'pricing.enterprise.button': '联系销售',
                'pricing.note': '仅在成功合作时抽佣 5%，无隐藏费用',
                
                // FAQ
                'faq.title': '常见问题',
                'faq.subtitle': '我们为您解答所有疑问',
                
                // 演示区域
                'demo.instruction': '粘贴商品链接，AI立即开始分析',
                'demo.placeholder': 'https://example.com/product...',
                'demo.analyze': '分析',
                'demo.result1': '匹配 <span class="font-bold">千万+</span> 位博主',
                'demo.result2': '生成个性化邮件',
                'demo.result3': '预测 ROI <span class="font-bold">180%</span>',
                
                // AI 智能分析
                'analysis.title': 'AI 驱动的智能分析',
                'analysis.subtitle': '深度洞察，让每一次建联都精准有效',
                
                // 工作流程
                'workflow.title': '简单五步，开启智能建联',
                'workflow.subtitle': '从注册到成功合作，AI 全程陪伴',
                'workflow.step1.title': '创建账户',
                'workflow.step1.description': '快速注册开始',
                'workflow.step2.title': '粘贴商品',
                'workflow.step2.description': '链接或上传商品',
                'workflow.step3.title': 'AI 匹配',
                'workflow.step3.description': '智能推荐博主',
                'workflow.step4.title': '发送邮件',
                'workflow.step4.description': '个性化触达',
                'workflow.step5.title': '追踪优化',
                'workflow.step5.description': '效果实时监控',
                
                // 社会证明
                'social.title': '深受品牌信赖',
                'social.subtitle': '已帮助 <span class="text-purple-400 font-bold">500+</span> 品牌成功出海',
                
                // 解决方案痛点
                'solution.problems.title': '传统方式的困扰',
                'solution.problems.item1': '人工查找博主费时费力，效率极低',
                'solution.problems.item2': '邮件模板千篇一律，回复率惨淡',
                'solution.problems.item3': '沟通进度分散，难以追踪管理',
                'solution.problems.item4': '效果难以衡量，ROI 不透明',
                'solution.ai.title': 'AI 驱动的解决方案',
                'solution.ai.item1': 'AI 秒级检索 千万+ 博主库，智能过滤',
                'solution.ai.item2': '大模型生成个性化邮件，自动 A/B 迭代',
                'solution.ai.item3': '全流程看板 + 漏斗视图，阶段可视化',
                'solution.ai.item4': '实时 KPI 仪表盘，ROI 一目了然',
                
                // 统计数据
                'stats.engagement': '互动增长',
                'stats.timeSaved': '时间节省',
                'stats.responseRate': '回复率提升',
                'stats.accuracy': '匹配精准度',
                
                // AI分析节点
                'analysis.core': 'AI 核心',
                'analysis.node1': '商品分析',
                'analysis.node2': '博主匹配',
                'analysis.node3': '邮件优化',
                'analysis.node4': '效果追踪',
                'analysis.metrics.title': '实时性能指标',
                'analysis.metrics.accuracy': '博主匹配精准度',
                'analysis.metrics.openRate': '邮件打开率',
                'analysis.metrics.conversionRate': '回复转化率',
                'analysis.metrics.roi': 'ROI 表现',
                'analysis.metrics.note': 'AI 持续学习优化，每次建联都比上次更精准',
                
                // 功能卡片
                'feature4.title': '效果实时追踪',
                'feature4.description': '实时监控网红回信状态，智能分析合作意向强度，建立效果追踪体系',
                'feature4.tag': '数据实时更新',
                'feature5.title': '全流程仪表盘',
                'feature5.description': '活跃合作、回复率、转化漏斗，数据驱动决策',
                'feature5.tag': '实时数据更新',
                'feature6.title': 'AI 智能优化',
                'feature6.description': '机器学习持续优化邮件模板，动态调整发送策略',
                'feature6.tag': '持续自动优化',
                
                // 客户案例
                'testimonials.user1.name': '张经理',
                'testimonials.user1.title': '跨境电商创始人',
                'testimonials.user1.quote': '"首次使用就带来了 <span class="text-purple-400 font-bold">6位数</span> 的销售额！AI 匹配的博主质量远超预期。"',
                'testimonials.user2.name': '李总监',
                'testimonials.user2.title': '品牌营销负责人',
                'testimonials.user2.quote': '"团队效率提升 <span class="text-purple-400 font-bold">200%</span>，再也不用熬夜找博主了！"',
                'testimonials.user3.name': '王CEO',
                'testimonials.user3.title': 'DTC品牌创始人',
                'testimonials.user3.quote': '"ROI 达到 <span class="text-purple-400 font-bold">180%</span>，这是我们做过最成功的营销投资！"',
                
                // 价格功能列表
                'pricing.starter.feature1': '月度 50 封建联',
                'pricing.starter.feature2': '基础博主匹配',
                'pricing.starter.feature3': '单人席位',
                'pricing.starter.feature4': '高级数据分析',
                'pricing.pro.feature1': '月度 1000 封建联',
                'pricing.pro.feature2': '高级筛选功能',
                'pricing.pro.feature3': '5 人团队协作',
                'pricing.pro.feature4': '实时数据分析',
                'pricing.enterprise.feature1': '不限建联数量',
                'pricing.enterprise.feature2': '不限团队席位',
                'pricing.enterprise.feature3': 'API 接入',
                'pricing.enterprise.feature4': '专属客户经理',
                
                // FAQ
                'faq.q1.question': 'Google 登录安全吗？',
                'faq.q1.answer': '我们使用 OAuth 2.0 标准认证，不会存储您的密码。所有数据传输均采用 SSL 加密，符合 GDPR 和 ISO-27001 安全标准。',
                'faq.q2.question': '数据源从哪里来？多久更新？',
                'faq.q2.answer': '我们整合了 YouTube、Instagram、TikTok 等主流平台的公开数据，拥有 千万+ 活跃博主库，每周更新，确保数据新鲜度。',
                'faq.q3.question': '如何保证博主真实有效？',
                'faq.q3.answer': 'AI 会分析博主的互动率、粉丝增长曲线、内容质量等多维度数据，自动过滤虚假账号，确保匹配的都是高质量创作者。',
                'faq.q4.question': '是否支持 TikTok / Instagram？',
                'faq.q4.answer': '未来计划支持！我们目前专注于 YouTube 平台，未来将逐步接入 TikTok、Instagram、Twitter 等更多平台，满足您的全渠道营销需求。',
                'faq.q5.question': 'AI 匹配效果如何保证？',
                'faq.q5.answer': 'AI 会持续学习和优化匹配算法，结合历史合作数据、受众重合度、内容风格匹配等多个维度，确保推荐精准度不断提升。',
                
                // 最终 CTA
                'final.cta.title': '准备好革新您的网红营销了吗？',
                'final.cta.description': '加入 500+ 成功品牌，让 AI 为您的出海之路保驾护航',
                'final.cta.note': '无需信用卡 · 申请免费试用 · 随时取消',
                
                // Footer
                'footer.contact.title': '联系我们',
                'footer.copyright': '© 2025 Turing Market. All rights reserved.',
            },
            en: {
                // 页面基本信息
                'page.title': 'Influencer Marketing Agent - AI-Powered Influencer Outreach Manager',
                'brand.name': 'Influencer Marketing Agent',
                
                // 导航
                'nav.features': 'Features',
                'nav.solutions': 'Solutions',
                'nav.pricing': 'Pricing',
                'cta.start': 'Apply for Free Trial',
                'cta.startNow': 'Apply for Free Trial',
                
                // Hero 部分
                'hero.title': 'All-in-One AI Influencer Marketing Platform',
                'hero.subtitle': 'Analyze Products, Match Creators, Auto Follow-up in One Click',
                'hero.description': 'Reduce <span class="text-purple-400 font-bold">75%</span> screening time, increase <span class="text-purple-400 font-bold">5x</span> response rate, let AI take over your influencer marketing workflow',
                
                // 解决方案
                'solution.title': 'Say Goodbye to Traditional Outreach Pain',
                'solution.subtitle': 'We use AI to solve every challenge',
                
                // 功能
                'features.title': 'Powerful Features, Smart & Efficient',
                'features.subtitle': 'Six core modules covering the entire outreach process',
                'feature1.title': 'AI Product Analysis',
                'feature1.description': 'Paste product links, automatically extract titles, images, specifications, understand your product instantly',
                'feature2.title': 'Smart Influencer Matching',
                'feature2.description': 'Similarity algorithms + audience profiling filters to precisely find the most suitable creators',
                'feature3.title': 'One-Click Batch Outreach',
                'feature3.description': 'Email generation, batch sending, intention follow-up, fully automated processing',
                'feature1.tag': '≤30s completion',
                'feature2.tag': '92% matching accuracy',
                'feature3.tag': '45%+ average response rate',
                
                // 价格
                'pricing.title': 'Transparent Pricing, Choose What You Need',
                'pricing.subtitle': 'Zero upfront cost, pay only for results',
                'pricing.starter.name': 'Starter',
                'pricing.starter.description': 'Perfect for individuals and small teams',
                'pricing.starter.price': 'Free',
                'pricing.pro.name': 'Pro',
                'pricing.pro.description': 'For team small-scale campaigns',
                'pricing.pro.price': 'Coming Soon',
                'pricing.enterprise.name': 'Enterprise',
                'pricing.enterprise.description': 'For agencies and large brands',
                'pricing.enterprise.price': 'Custom',
                'pricing.starter.button': 'Get Started',
                'pricing.pro.button': 'Upgrade Now',
                'pricing.enterprise.button': 'Contact Sales',
                'pricing.note': 'Only 5% commission on successful collaborations, no hidden fees',
                
                // FAQ
                'faq.title': 'Frequently Asked Questions',
                'faq.subtitle': 'We answer all your questions',
                
                // 演示区域
                'demo.instruction': 'Paste product link, AI starts analysis immediately',
                'demo.placeholder': 'https://example.com/product...',
                'demo.analyze': 'Analyze',
                'demo.result1': 'Match <span class="font-bold">10M+</span> influencers',
                'demo.result2': 'Generate personalized emails',
                'demo.result3': 'Predict ROI <span class="font-bold">180%</span>',
                
                // AI 智能分析
                'analysis.title': 'AI-Driven Smart Analysis',
                'analysis.subtitle': 'Deep insights make every outreach precise and effective',
                
                // 工作流程
                'workflow.title': 'Simple 5 Steps to Smart Outreach',
                'workflow.subtitle': 'From registration to successful collaboration, AI accompanies you all the way',
                'workflow.step1.title': 'Create Account',
                'workflow.step1.description': 'Quick registration to start',
                'workflow.step2.title': 'Paste Product',
                'workflow.step2.description': 'Link or upload product',
                'workflow.step3.title': 'AI Matching',
                'workflow.step3.description': 'Smart influencer recommendations',
                'workflow.step4.title': 'Send Emails',
                'workflow.step4.description': 'Personalized outreach',
                'workflow.step5.title': 'Track & Optimize',
                'workflow.step5.description': 'Real-time performance monitoring',
                
                // 社会证明
                'social.title': 'Trusted by Brands',
                'social.subtitle': 'Helped <span class="text-purple-400 font-bold">500+</span> brands succeed globally',
                
                // 解决方案痛点
                'solution.problems.title': 'Traditional Outreach Pain Points',
                'solution.problems.item1': 'Manual influencer search is time-consuming and inefficient',
                'solution.problems.item2': 'Generic email templates lead to poor response rates',
                'solution.problems.item3': 'Scattered communication progress, difficult to track',
                'solution.problems.item4': 'Hard to measure effectiveness, unclear ROI',
                'solution.ai.title': 'AI-Driven Solutions',
                'solution.ai.item1': 'AI searches 10M+ influencer database in seconds with smart filtering',
                'solution.ai.item2': 'Large language models generate personalized emails with automatic A/B testing',
                'solution.ai.item3': 'Full-process kanban + funnel view for stage visualization',
                'solution.ai.item4': 'Real-time KPI dashboard, ROI at a glance',
                
                // 统计数据
                'stats.engagement': 'Engagement Growth',
                'stats.timeSaved': 'Time Saved',
                'stats.responseRate': 'Response Rate Boost',
                'stats.accuracy': 'Matching Accuracy',
                
                // AI分析节点
                'analysis.core': 'AI Core',
                'analysis.node1': 'Product Analysis',
                'analysis.node2': 'Influencer Matching',
                'analysis.node3': 'Email Optimization',
                'analysis.node4': 'Performance Tracking',
                'analysis.metrics.title': 'Real-time Performance Metrics',
                'analysis.metrics.accuracy': 'Influencer Matching Accuracy',
                'analysis.metrics.openRate': 'Email Open Rate',
                'analysis.metrics.conversionRate': 'Reply Conversion Rate',
                'analysis.metrics.roi': 'ROI Performance',
                'analysis.metrics.note': 'AI continuously learns and optimizes, making each outreach more precise than the last',
                
                // 功能卡片
                'feature4.title': 'Real-time Performance Tracking',
                'feature4.description': 'Real-time monitoring of influencer responses, intelligent analysis of collaboration intent, comprehensive performance tracking system',
                'feature4.tag': 'Real-time data updates',
                'feature5.title': 'Full-Process Dashboard',
                'feature5.description': 'Active collaborations, response rates, conversion funnels, data-driven decisions',
                'feature5.tag': 'Real-time data updates',
                'feature6.title': 'AI Smart Optimization',
                'feature6.description': 'Machine learning continuously optimizes email templates, dynamically adjusts sending strategies',
                'feature6.tag': 'Continuous auto optimization',
                
                // 客户案例
                'testimonials.user1.name': 'Manager Zhang',
                'testimonials.user1.title': 'Cross-border E-commerce Founder',
                'testimonials.user1.quote': '"First use brought <span class="text-purple-400 font-bold">6-figure</span> sales! AI-matched influencer quality exceeded expectations."',
                'testimonials.user2.name': 'Director Li',
                'testimonials.user2.title': 'Brand Marketing Director',
                'testimonials.user2.quote': '"Team efficiency improved by <span class="text-purple-400 font-bold">200%</span>, no more staying up late to find influencers!"',
                'testimonials.user3.name': 'CEO Wang',
                'testimonials.user3.title': 'DTC Brand Founder',
                'testimonials.user3.quote': '"ROI reached <span class="text-purple-400 font-bold">180%</span>, this is our most successful marketing investment!"',
                
                // 价格功能列表
                'pricing.starter.feature1': '50 outreach emails per month',
                'pricing.starter.feature2': 'Basic influencer matching',
                'pricing.starter.feature3': 'Single user seat',
                'pricing.starter.feature4': 'Advanced data analytics',
                'pricing.pro.feature1': '1,000 outreach emails per month',
                'pricing.pro.feature2': 'Advanced filtering features',
                'pricing.pro.feature3': '5-person team collaboration',
                'pricing.pro.feature4': 'Real-time data analytics',
                'pricing.enterprise.feature1': 'Unlimited outreach emails',
                'pricing.enterprise.feature2': 'Unlimited team seats',
                'pricing.enterprise.feature3': 'API access',
                'pricing.enterprise.feature4': 'Dedicated account manager',
                
                // FAQ
                'faq.q1.question': 'Is Google login secure?',
                'faq.q1.answer': 'We use OAuth 2.0 standard authentication and do not store your password. All data transmission uses SSL encryption, compliant with GDPR and ISO-27001 security standards.',
                'faq.q2.question': 'Where does the data come from? How often is it updated?',
                'faq.q2.answer': 'We integrate public data from mainstream platforms like YouTube, Instagram, TikTok, with 10M+ active influencer database updated weekly to ensure data freshness.',
                'faq.q3.question': 'How do you ensure influencers are authentic and effective?',
                'faq.q3.answer': 'AI analyzes multi-dimensional data including engagement rates, follower growth curves, content quality to automatically filter fake accounts, ensuring matched creators are high-quality.',
                'faq.q4.question': 'Do you support TikTok / Instagram?',
                'faq.q4.answer': 'Future planned support! We currently focus on YouTube platform and will gradually integrate TikTok, Instagram, Twitter and more platforms to meet your omnichannel marketing needs.',
                'faq.q5.question': 'How do you guarantee AI matching effectiveness?',
                'faq.q5.answer': 'AI continuously learns and optimizes matching algorithms, combining historical collaboration data, audience overlap, content style matching across multiple dimensions to ensure recommendation accuracy keeps improving.',
                
                // 最终 CTA
                'final.cta.title': 'Ready to revolutionize your influencer marketing?',
                'final.cta.description': 'Join 500+ successful brands, let AI power your global expansion journey',
                'final.cta.note': 'No credit card required · Apply for free trial · Cancel anytime',
                
                // Footer
                'footer.contact.title': 'Contact Us',
                'footer.copyright': '© 2025 Turing Market. All rights reserved.',
            }
        };

        // 当前语言
        let currentLanguage = 'zh';

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            
            // 更新所有带有 data-lang-key 的元素
            document.querySelectorAll('[data-lang-key]').forEach(element => {
                const key = element.getAttribute('data-lang-key');
                if (languages[lang] && languages[lang][key]) {
                    // 特殊处理 title 标签
                    if (element.tagName === 'TITLE') {
                        element.textContent = languages[lang][key];
                    }
                    // 特殊处理 input 的 placeholder
                    else if (element.tagName === 'INPUT' && element.hasAttribute('placeholder')) {
                        element.placeholder = languages[lang][key];
                    }
                    // 普通元素使用 innerHTML
                    else {
                        element.innerHTML = languages[lang][key];
                    }
                }
            });
            
            // 更新语言选择器显示
            const currentLangElement = document.getElementById('currentLang');
            currentLangElement.textContent = lang === 'zh' ? '中文' : 'English';
            
            // 更新下拉菜单中的选中状态
            document.querySelectorAll('.language-option').forEach(option => {
                const optionLang = option.getAttribute('data-lang');
                if (optionLang === lang) {
                    option.classList.add('bg-gray-700');
                } else {
                    option.classList.remove('bg-gray-700');
                }
            });
            
            // 更新 HTML lang 属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
            
            // 保存语言偏好到本地存储
            localStorage.setItem('preferred-language', lang);
            
            // 隐藏下拉菜单
            document.getElementById('languageDropdown').classList.add('hidden');
        }

        // 检测系统语言
        function detectSystemLanguage() {
            const systemLang = navigator.language || navigator.userLanguage;
            // 检查是否为中文相关语言
            if (systemLang.startsWith('zh')) {
                return 'zh';
            }
            // 默认英文
            return 'en';
        }

        // 初始化语言
        function initLanguage() {
            // 优先级：本地存储 > 系统语言 > 默认中文
            const savedLanguage = localStorage.getItem('preferred-language');
            if (savedLanguage && languages[savedLanguage]) {
                switchLanguage(savedLanguage);
            } else {
                // 根据系统语言自动选择
                const systemLanguage = detectSystemLanguage();
                switchLanguage(systemLanguage);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initLanguage();
            
            // 立即显示首屏内容，避免等待滚动
            const viewportHeight = window.innerHeight;
            document.querySelectorAll('.scroll-reveal').forEach(el => {
                const rect = el.getBoundingClientRect();
                if (rect.top < viewportHeight) {
                    el.classList.add('active');
                }
            });
            
            // 语言切换按钮事件
            const languageToggle = document.getElementById('languageToggle');
            const languageDropdown = document.getElementById('languageDropdown');
            
            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });
            
            // 语言选项点击事件
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function() {
                    const lang = this.getAttribute('data-lang');
                    switchLanguage(lang);
                });
            });
            
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function() {
                languageDropdown.classList.add('hidden');
            });
        });

        // 性能优化：防抖函数
        function throttle(func, delay) {
            let timeoutId;
            let lastExecTime = 0;
            return function (...args) {
                const currentTime = Date.now();
                if (currentTime - lastExecTime > delay) {
                    func.apply(this, args);
                    lastExecTime = currentTime;
                } else {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        func.apply(this, args);
                        lastExecTime = Date.now();
                    }, delay - (currentTime - lastExecTime));
                }
            };
        }

        // 性能优化：缓存DOM查询
        const cachedElements = {
            navbar: document.querySelector('.navbar'),
            floatingCTA: document.querySelector('.floating-cta')
        };

        // 性能优化：合并滚动事件监听器
        const handleScroll = throttle(function() {
            const scrollY = window.scrollY;
            
            // 导航栏滚动效果
            if (scrollY > 50) {
                cachedElements.navbar.classList.add('scrolled');
            } else {
                cachedElements.navbar.classList.remove('scrolled');
            }
            
            // 悬浮CTA按钮显示逻辑
            if (cachedElements.floatingCTA) {
                if (scrollY > 800) {
                    cachedElements.floatingCTA.classList.add('show');
                } else {
                    cachedElements.floatingCTA.classList.remove('show');
                }
            }
        }, 16); // 约60fps

        window.addEventListener('scroll', handleScroll, { passive: true });
        
        // 快速滑动优化：检测滑动速度并预加载
        let lastScrollTop = 0;
        let scrollSpeed = 0;
        let scrollTimer = null;
        
        const detectScrollSpeed = throttle(function() {
            const currentScrollTop = window.scrollY;
            scrollSpeed = Math.abs(currentScrollTop - lastScrollTop);
            
            // 如果滑动速度很快，增加预加载范围
            if (scrollSpeed > 50) {
                // 临时增加观察范围
                const tempOptions = {
                    threshold: 0.01,
                    rootMargin: '500px 0px 500px 0px' // 快速滑动时增加到500px
                };
                
                // 预加载更多内容
                document.querySelectorAll('.scroll-reveal:not(.active)').forEach(el => {
                    const rect = el.getBoundingClientRect();
                    if (rect.top < window.innerHeight + 500) {
                        el.classList.add('active');
                    }
                });
            }
            
            lastScrollTop = currentScrollTop;
            
            // 重置滑动速度
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(() => {
                scrollSpeed = 0;
            }, 150);
        }, 50);
        
        window.addEventListener('scroll', detectScrollSpeed, { passive: true });

        // 性能优化：统一的IntersectionObserver - 优化快速滑动加载
        const observerOptions = {
            threshold: 0.01, // 降低触发阈值，元素露出1%即触发
            rootMargin: '200px 0px 200px 0px' // 提前200px开始加载，确保快速滑动时内容能及时显示
        };

        const unifiedObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 滚动显示动画
                    if (entry.target.classList.contains('scroll-reveal') && !entry.target.classList.contains('active')) {
                        // 使用requestAnimationFrame确保动画流畅
                        requestAnimationFrame(() => {
                            entry.target.classList.add('active');
                        });
                        
                        // 移除观察，避免重复触发
                        unifiedObserver.unobserve(entry.target);
                    }
                    
                    // 统计数字动画
                    if (entry.target.classList.contains('stat-number') && !entry.target.animated) {
                        entry.target.animated = true;
                        const text = entry.target.textContent;
                        const match = text.match(/(\d+)/);
                        if (match) {
                            const endValue = parseInt(match[1]);
                            const suffix = text.replace(match[0], '');
                            entry.target.textContent = '0' + suffix;
                            
                            // 使用requestAnimationFrame优化动画
                            requestAnimationFrame(() => {
                                setTimeout(() => {
                                    animateValue(entry.target, 0, endValue, 2000);
                                }, 200);
                            });
                        }
                        
                        // 移除观察
                        unifiedObserver.unobserve(entry.target);
                    }
                }
            });
        }, observerOptions);

        // 性能优化：批量观察元素
        const elementsToObserve = document.querySelectorAll('.scroll-reveal, .stat-number');
        
        // 使用requestIdleCallback优化初始化
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => {
                elementsToObserve.forEach(el => {
                    unifiedObserver.observe(el);
                });
            });
        } else {
            elementsToObserve.forEach(el => {
                unifiedObserver.observe(el);
            });
        }
        
        // 兜底方案：确保所有内容最终都能显示
        setTimeout(() => {
            document.querySelectorAll('.scroll-reveal:not(.active)').forEach(el => {
                el.classList.add('active');
            });
        }, 3000); // 3秒后强制显示所有未显示的内容

        // 性能优化：事件委托 - 平滑滚动
        document.addEventListener('click', function(e) {
            const anchor = e.target.closest('a[href^="#"]');
            if (anchor) {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });

        // 性能优化：事件委托 - FAQ 展开/收起动画
        document.addEventListener('toggle', function(e) {
            if (e.target.tagName === 'DETAILS') {
                const icon = e.target.querySelector('.fa-chevron-down');
                if (icon) {
                    icon.style.transform = e.target.open ? 'rotate(180deg)' : 'rotate(0deg)';
                }
            }
        });

        // 数字动画
        function animateValue(element, start, end, duration) {
            let startTimestamp = null;
            const step = (timestamp) => {
                if (!startTimestamp) startTimestamp = timestamp;
                const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                element.textContent = Math.floor(progress * (end - start) + start) + '%';
                if (progress < 1) {
                    window.requestAnimationFrame(step);
                }
            };
            window.requestAnimationFrame(step);
        }



    </script>

    <!-- 
    按钮配置说明:
    1. 所有按钮都会直接跳转到统一地址: https://demo.leadcoins.club
    2. 包括导航栏、Hero区域、定价、分析等所有按钮
    3. 点击后直接在当前页面跳转到目标地址，无需确认
    4. 如需修改跳转地址，请修改 REDIRECT_URL 变量
    -->
</body>
</html>