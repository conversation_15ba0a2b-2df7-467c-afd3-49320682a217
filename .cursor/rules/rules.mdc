---
description: 
globs: 
alwaysApply: false
---
# 跨境运营助手项目规则

## 多语言功能规范

### 语言支持
- 支持中文（zh）和英文（en）两种语言
- 默认语言为中文
- 用户偏好保存在本地存储中

### HTML 标记规范
- 所有需要翻译的元素必须添加 `data-lang-key` 属性
- 语言键使用点分隔的层级结构，如 `nav.features`、`hero.title`
- 特殊元素处理：
  - `<title>` 标签：直接修改 textContent
  - `<input>` 的 placeholder：修改 placeholder 属性
  - 其他元素：修改 innerHTML

### JavaScript 语言数据结构
- 语言数据存储在 `languages` 对象中
- 每种语言对应一个子对象
- 支持 HTML 标签在翻译内容中（除 title 和 placeholder）

### 语言切换功能
- 导航栏提供语言选择器
- 点击切换时自动更新所有标记元素
- 更新页面 lang 属性和当前语言显示
- 保存用户偏好到 localStorage

### 命名规范
- 导航相关：`nav.*`
- CTA 按钮：`cta.*`
- 主要区块：`hero.*`、`features.*`、`pricing.*`、`faq.*`
- 功能介绍：`feature1.*`、`feature2.*` 等
- 工作流程：`workflow.*`
- 演示区域：`demo.*`

### 浏览器兼容性
- 支持现代浏览器的 localStorage API
- 使用标准的 DOM 操作方法
- 支持 ES6+ 语法
