<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-key="page_title">企业邮箱配置指南 - 网红营销智能体</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.staticfile.net">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Outfit:wght@300;400;500;600;700;800&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="dns-prefetch" href="//demo.leadcoins.club">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --dark-bg: #0f0f1e;
            --card-bg: rgba(26, 27, 46, 0.8);
            --accent-purple: #7c3aed;
            --accent-blue: #4f46e5;
            --text-primary: #ffffff;
            --text-secondary: #a5a6b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--dark-bg);
            color: var(--text-primary);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* 动态背景效果 */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.3;
            animation: float 20s infinite ease-in-out;
        }

        .orb-1 {
            width: 600px;
            height: 600px;
            background: var(--accent-purple);
            top: -300px;
            right: -200px;
        }

        .orb-2 {
            width: 400px;
            height: 400px;
            background: var(--accent-blue);
            bottom: -200px;
            left: -100px;
            animation-delay: -5s;
        }

        .orb-3 {
            width: 500px;
            height: 500px;
            background: #ec4899;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            33% { transform: translate(30px, -30px) scale(1.1); }
            66% { transform: translate(-20px, 20px) scale(0.9); }
        }

        /* 导航栏 */
        .navbar {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(15, 15, 30, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
            background: rgba(15, 15, 30, 0.98);
        }

        /* 按钮样式 */
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            padding: 12px 28px;
            border-radius: 50px;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(124, 58, 237, 0.4);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        /* 主要内容区域 */
        .main-content {
            position: relative;
            z-index: 10;
            min-height: 100vh;
            padding-top: 2rem;
        }

        /* 页面标题 */
        .page-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 600;
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
            line-height: 1.2;
            background: linear-gradient(to right, #ffffff, #a5a6b8);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease-out;
        }

        /* 标题字体优化 */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
        }

        /* 字重优化 - 减轻粗重感 */
        body {
            font-weight: 400; /* 确保正文使用正常字重 */
        }
        
        .font-bold {
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
            font-weight: 600 !important; /* 从700调整到600 */
        }
        
        .font-semibold {
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
            font-weight: 500 !important; /* 从600调整到500 */
        }
        
        .font-medium {
            font-weight: 500 !important;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 面包屑导航 */
        .breadcrumb {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 3rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .breadcrumb a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .breadcrumb a:hover {
            color: var(--text-primary);
        }

        .breadcrumb-separator {
            margin: 0 0.5rem;
            color: rgba(255, 255, 255, 0.3);
        }

        /* 内容卡片 */
        .content-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .content-card:hover {
            transform: translateY(-5px);
            border-color: rgba(124, 58, 237, 0.5);
            box-shadow: 0 20px 40px rgba(124, 58, 237, 0.2);
        }

        /* 标题样式 */
        .section-title {
            font-size: 1.5rem;
            font-weight: 500;
            font-family: 'Outfit', 'Noto Sans SC', 'PingFang SC', sans-serif;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            position: relative;
            padding-left: 1rem;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            border-radius: 2px;
        }

        /* 表格样式 */
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            background: rgba(26, 27, 46, 0.6);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .config-table th {
            background: var(--primary-gradient);
            color: white;
            padding: 1rem;
            text-align: left;
            font-weight: 500;
        }

        .config-table td {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            vertical-align: top;
        }

        .config-table tr:last-child td {
            border-bottom: none;
        }

        .config-table tr:hover {
            background: rgba(124, 58, 237, 0.1);
        }

        /* 链接样式 */
        .content-link {
            color: #60a5fa;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .content-link:hover {
            color: #93c5fd;
            text-decoration: underline;
        }

        /* 步骤列表 */
        .steps-list {
            list-style: none;
            counter-reset: step-counter;
        }

        .steps-list li {
            counter-increment: step-counter;
            margin-bottom: 1.5rem;
            padding-left: 3rem;
            position: relative;
        }

        .steps-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            width: 2rem;
            height: 2rem;
            background: var(--primary-gradient);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.875rem;
        }

        /* 警告框 */
        .warning-box {
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            position: relative;
        }

        .warning-box::before {
            content: '⚠️';
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }

        /* 信息框 */
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            position: relative;
        }

        .info-box::before {
            content: 'ℹ️';
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }


        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-card {
                padding: 2rem 1.5rem;
            }
            
            .config-table {
                font-size: 0.875rem;
            }
            
            .config-table th,
            .config-table td {
                padding: 0.75rem 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="animated-bg">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar py-4 relative z-50">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-rocket text-2xl" style="color: var(--accent-purple);"></i>
                    <span class="text-xl font-bold" data-key="nav_brand">网红营销智能体</span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- 语言切换 -->
                    <div class="relative">
                        <button id="languageToggle" class="flex items-center space-x-2 text-gray-200 hover:text-white transition">
                            <i class="fas fa-globe"></i>
                            <span id="currentLang">中文</span>
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <div id="languageDropdown" class="absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg hidden min-w-[140px] z-50">
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="zh">
                                <i class="fas fa-flag mr-2 text-red-400"></i>
                                <span>中文</span>
                            </button>
                            <button class="w-full px-4 py-2 text-left hover:bg-gray-700 transition language-option flex items-center" data-lang="en">
                                <i class="fas fa-flag mr-2 text-blue-400"></i>
                                <span>English</span>
                            </button>
                        </div>
                    </div>
                    
                    <button class="btn-primary" onclick="handleButtonClick()" data-key="nav_trial_btn">
                        申请免费试用
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-content">
        <div class="container mx-auto px-6 py-8">

            <!-- 页面标题 -->
            <h1 class="page-title" data-key="main_title">企业邮箱配置指南</h1>

            <div class="max-w-4xl mx-auto">
                <!-- 主要内容区域 -->
                    <!-- 概述 -->
                    <div class="content-card">
                        <div class="mb-6">
                            <p class="text-lg text-gray-200 mb-4" data-key="overview_p1">
                                想通过企业邮箱来邀约网红，可以授权给第三方客户端（例如网红营销智能体平台）登录企业邮箱来发送邮件。
                            </p>
                            <p class="text-gray-200 mb-4" data-key="overview_p2">
                                授权只用于调用企业邮箱的发送邮件功能，本质上群发的邮件还是通过用户的企业邮箱发出，因此会受限于企业邮箱的发件规则。
                            </p>
                            <div class="warning-box">
                                <strong data-key="warning_label">注意：</strong><span data-key="warning_text">目前网红营销智能体支持绑定大多数企业邮箱，但Outlook邮箱由于自身服务在支持SMTP时的稳定性问题，可能存在绑定失败的情况，因此我们建议优先选用其他企业邮件服务商。</span>
                            </div>
                        </div>
                    </div>

                    <!-- 授权方法 -->
                    <div class="content-card">
                        <h2 class="section-title" data-key="section_auth_title">一、授权方法</h2>
                        
                        <div class="space-y-4">
                            <div>
                                <strong data-key="step1_title">1. 进入邮箱配置</strong><br>
                                <div class="ml-4 mt-2" data-key="step1_desc">点击个人头像 → 账户设置 → 进入邮件配置界面</div>
                            </div>
                            
                            <div>
                                <strong data-key="step2_title">2. 选择其他邮箱</strong><br>
                                <div class="ml-4 mt-2">
                                    <span data-key="step2_desc">选择【其他邮箱】后输入以下信息：</span>
                                    <div class="mt-2 ml-4 space-y-1">
                                        <div data-key="email_address_info">• <strong>邮箱地址：</strong>填写企业邮箱地址</div>
                                        <div data-key="password_info">• <strong>密码：</strong>填写企业邮箱登录密码或客户端授权码</div>
                                    </div>
                                    
                                    <div class="info-box mt-4">
                                        <strong data-key="important_reminder_label">重要提醒：</strong><span data-key="important_reminder_text">网易企业邮箱、QQ邮箱、飞书企业邮箱、腾讯企业邮箱需要使用【授权码】登录。授权码是企业邮箱为第三方客户端登录而生成的专用密码，请在【密码】处填入授权码，而非邮箱登录密码。授权码获取方法详见下方【SMTP配置帮助文档】。</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <strong data-key="step3_title">3. 高级选项配置</strong><br>
                                <div class="ml-4 mt-2">
                                    <span data-key="step3_desc">请根据各自企业邮箱的需求，手动填写以下配置：</span>
                                    <div class="mt-2 ml-4 space-y-1">
                                        <div data-key="smtp_server_info">• <strong>SMTP服务器：</strong>邮件发送服务器地址</div>
                                        <div data-key="imap_server_info">• <strong>IMAP服务器：</strong>邮件接收服务器地址</div>
                                        <div data-key="protocol_info">• <strong>协议：</strong>选择对应的加密协议（SSL/TLS）</div>
                                        <div data-key="port_info">• <strong>端口：</strong>SMTP和IMAP对应的端口号</div>
                                    </div>
                                    <div class="mt-3">
                                        <span data-key="config_reference"><strong>要求详细查看下方【SMTP配置帮助文档】</strong>，根据您使用的企业邮箱服务商查找对应的配置参数。</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SMTP配置帮助文档 -->
                    <div class="content-card">
                        <h2 class="section-title" data-key="section_smtp_title">二、SMTP配置帮助文档</h2>
                        
                        <p class="text-gray-200 mb-6" data-key="smtp_intro">
                            不同的企业邮箱如何开启、配置SMTP服务器以及获取授权码，方法可参考用户所使用的邮件服务商的帮助文档。以下整理了部分常用的企业邮箱的帮助文档，在高级选项默认配置下无法授权成功时，请按相关文档检查配置并验证。
                        </p>

                        <table class="config-table">
                            <thead>
                                <tr>
                                    <th style="width: 22%;" data-key="table_provider">邮箱服务商</th>
                                    <th style="width: 20%;" data-key="table_smtp_server">SMTP服务器</th>
                                    <th style="width: 18%;" data-key="table_auth_code">需要授权码</th>
                                    <th style="width: 40%;" data-key="table_config_doc">配置文档</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong data-key="provider_tencent">腾讯企业邮箱</strong></td>
                                    <td>smtp.exmail.qq.com</td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://open.work.weixin.qq.com/help2/pc/19886?person_id=1&subtype=1&id=20095&no=1001570" class="content-link" target="_blank" data-key="tencent_doc1">开启POP/SMTP/IMAP功能</a><br>
                                        <a href="https://open.work.weixin.qq.com/help2/pc/20038" class="content-link" target="_blank" data-key="tencent_doc2">收发信返回错误码</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_qq">QQ邮箱</strong></td>
                                    <td>smtp.qq.com</td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://service.mail.qq.com/detail/0/75" class="content-link" target="_blank" data-key="qq_doc">QQ邮箱授权码配置</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_netease">网易邮箱</strong><br><small data-key="netease_desc">(企业邮箱/126/163邮箱等)</small></td>
                                    <td>
                                        <a href="https://qiye.163.com/help/client-profile.html" class="content-link" target="_blank" data-key="netease_smtp">查询SMTP服务器</a>
                                    </td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://qiye.163.com/help/af988e.html" class="content-link" target="_blank" data-key="netease_doc1">网易企业邮箱授权码</a><br>
                                        <a href="https://qiye.163.com/help/l-9.html" class="content-link" target="_blank" data-key="netease_doc2">退信常见原因</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_feishu">飞书企业邮箱</strong></td>
                                    <td>smtp.feishu.cn</td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://www.feishu.cn/hc/zh-CN/articles/360049068017" class="content-link" target="_blank" data-key="feishu_doc1">启用第三方客户端登录及生成专用密码</a><br>
                                        <a href="https://www.feishu.cn/hc/zh-CN/articles/654962037455" class="content-link" target="_blank" data-key="feishu_doc2">邮箱常见退信报错和解决方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_lark">Lark企业邮箱</strong></td>
                                    <td>smtp.larksuite.com</td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://www.larksuite.com/hc/en-US/articles/360048488295" class="content-link" target="_blank" data-key="lark_doc">Use Lark Mail on third-party email clients</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_ali">阿里企业邮箱</strong></td>
                                    <td>smtp.qiye.aliyun.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://help.aliyun.com/document_detail/606337.html" class="content-link" target="_blank" data-key="ali_doc1">开启SMTP服务</a><br>
                                        <a href="https://help.aliyun.com/document_detail/36576.html" class="content-link" target="_blank" data-key="ali_doc2">配置到第三方客户端</a><br>
                                        <a href="https://help.aliyun.com/document_detail/36612.html" class="content-link" target="_blank" data-key="ali_doc3">常见退信报错和解决方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_outlook">Outlook企业邮箱</strong><br><small style="color: #fbbf24;" data-key="outlook_unstable">(不稳定)</small></td>
                                    <td>smtp-mail.outlook.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://support.microsoft.com/en-us/office/pop-imap-and-smtp-settings-for-outlook-com-d088b986-291d-42b8-9564-9c414e2aa040" class="content-link" target="_blank" data-key="outlook_doc1">Outlook SMTP配置文档</a><br>
                                        <a href="https://outlook.live.com/mail/0/options/mail/accounts/popImap" class="content-link" target="_blank" data-key="outlook_doc2">Outlook SMTP设置界面（不要勾选ssl，并打开POP）</a><br>
                                        <a href="https://learn.microsoft.com/zh-cn/exchange/troubleshoot/email-delivery/ndr/non-delivery-reports-in-exchange-online" class="content-link" target="_blank" data-key="outlook_doc3">Outlook错误码</a><br>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_sina">新浪企业邮箱</strong></td>
                                    <td>smtp.sina.net</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://mail.sina.net/enthelp.php?act=client" class="content-link" target="_blank" data-key="sina_doc">客户端使用方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_sohu">搜狐企业邮箱</strong></td>
                                    <td>mail.sohu.net</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://mail.sohu.net/medias/help/help45.html" class="content-link" target="_blank" data-key="sohu_doc">开启SMTP服务</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_zoho">ZOHO企业邮箱</strong></td>
                                    <td>smtp.zoho.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://www.zoho.com.cn/mail/help/zoho-smtp.html" class="content-link" target="_blank" data-key="zoho_doc">配置SMTP服务器</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_amazon">亚马逊企业邮箱</strong></td>
                                    <td>smtp.mail.us-west-2.awsapps.com<br>smtp.mail.us-east-1.awsapps.com<br>smtp.mail.eu-west-1.awsapps.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://docs.aws.amazon.com/zh_cn/workmail/latest/userguide/using_IMAP.html" class="content-link" target="_blank" data-key="amazon_doc">为亚马逊WorkMail设置IMAP</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_perfect">完美邮箱</strong></td>
                                    <td>smtp.88.com</td>
                                    <td><span style="color: #fbbf24;" data-key="yes">是</span></td>
                                    <td>
                                        <a href="https://www.88.com/help/10019000079.html" class="content-link" target="_blank" data-key="perfect_doc">配置方法</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_dingtalk">钉钉邮箱</strong></td>
                                    <td>smtp.em.dingtalk.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://alidocs.dingtalk.com/i/p/Y7kmbokZp3pgGLq2/docs/Gl6Pm2Db8D3mzPQGsdMwy034JxLq0Ee4" class="content-link" target="_blank" data-key="dingtalk_doc">SMTP地址和端口信息</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_ionos">IONOS</strong></td>
                                    <td>smtp.ionos.com</td>
                                    <td><span style="color: #4ade80;" data-key="no">否</span></td>
                                    <td>
                                        <a href="https://www.ionos.com/help/email/general-topics/settings-for-your-email-programs-imap-pop3/?utm_source=search&utm_medium=standalone&utm_term=SMTP&utm_campaign=all" class="content-link" target="_blank" data-key="ionos_doc1">配置方法</a><br>
                                        <a href="https://www.ionos.com/help/email/postmaster/smtp-error-messages-of-the-11-ionos-mail-servers/?utm_source=search&utm_medium=standalone&utm_term=SMTP&utm_campaign=all" class="content-link" target="_blank" data-key="ionos_doc2">错误码查询</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong data-key="provider_yunyou">云邮</strong></td>
                                    <td>smtp.yunyou.top</td>
                                    <td><span style="color: #fbbf24;" data-key="yes_no">是/否</span></td>
                                    <td>
                                        <a href="https://www.yunyou.top/v2/help/detail?id=57" class="content-link" target="_blank" data-key="yunyou_doc1">常见企业邮箱POP/SMTP/IMAP服务器地址设置</a><br>
                                        <a href="https://www.yunyou.top/v2/help/detail?id=117" class="content-link" target="_blank" data-key="yunyou_doc2">开启/关闭客户端独立密码(授权码)</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </div>

                    <!-- 故障排除 -->
                    <div class="content-card">
                        <h2 class="section-title" data-key="section_troubleshoot_title">三、邮件验证失败的常见原因及解决方法</h2>
                        
                        <h3 class="text-xl font-semibold text-white mb-3 mt-6" data-key="troubleshoot_1_title">1. 邮箱密码填写错误</h3>
                        <p class="text-gray-200 mb-4" data-key="troubleshoot_1_desc">
                            请检查邮箱账号和密码是否正确。部分企业邮箱需要【授权码】登录，授权码是企业邮箱为登录第三方客户端而引入的专用密码，不是所有企业邮箱都需要授权码登录，但有授权码时需要在【密码】处填入授权码，而非企业邮箱的登录密码，授权码获取方法详见上方【<strong>SMTP配置帮助文档</strong>】。
                        </p>

                        <h3 class="text-xl font-semibold text-white mb-3 mt-6" data-key="troubleshoot_2_title">2. SMTP服务器配置错误</h3>
                        <p class="text-gray-200 mb-4" data-key="troubleshoot_2_desc">
                            与企业邮箱管理员确认使用的邮件服务商品牌，按该品牌的邮件服务帮助文档来配置，比如某个企业邮箱域名，但使用的其实是阿里企业邮箱服务，需要配置阿里企业邮箱的SMTP服务器。
                        </p>

                        <h3 class="text-xl font-semibold text-white mb-3 mt-6" data-key="troubleshoot_3_title">3. 企业的SMTP服务未开启</h3>
                        <p class="text-gray-200 mb-4" data-key="troubleshoot_3_desc">
                            邮箱地址、密码、SMTP服务器均检查配置无误，依然无法绑定成功时，有可能是企业邮箱开启了禁止使用三方客户端登录，此时需要与邮箱管理员沟通关闭禁止功能，关闭后再次尝试绑定，开启方法详见上方【<strong>SMTP配置帮助文档</strong>】。
                        </p>

                        <h3 class="text-xl font-semibold text-white mb-3 mt-6" data-key="troubleshoot_4_title">4. 用户个人邮箱账户开启了三方客户端安全密码</h3>
                        <p class="text-gray-200 mb-4" data-key="troubleshoot_4_desc">
                            在公司未禁止使用三方客户端登录的情况下绑定失败，可检查个人邮箱账户是否开启了三方客户端安全密码/授权码登录，如果开启了需要使用安全密码登录/授权码登录或者关闭三方客户端安全密码/授权码。
                        </p>

                        <div class="info-box">
                            <strong data-key="tip_label">提示：</strong><span data-key="troubleshoot_tip">为了验证是用户配置问题还是平台问题，可以尝试使用其他三方客户端用相同的配置信息绑定，比如使用Outlook客户端（Outlook客户端会自动给填写的邮箱解析出正确的SMTP配置信息）。</span>
                        </div>
                    </div>

                    <!-- 常见问题 -->
                    <div class="content-card">
                        <h2 class="section-title" data-key="section_faq_title">四、常见问题</h2>
                        
                        <div class="space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold text-white mb-2">
                                    <span class="text-purple-400" data-key="question_label">问：</span><span data-key="faq1_question">能否使用企业邮箱以外的个人邮箱发送？</span>
                                </h3>
                                <p class="text-gray-200 pl-8">
                                    <span class="text-green-400" data-key="answer_label">答：</span><span data-key="faq1_answer">如果不想使用企业邮箱发送，请参考其他个人邮箱（如qq、163等）对应邮箱配置。</span>
                                </p>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold text-white mb-2">
                                    <span class="text-purple-400" data-key="question_label">问：</span><span data-key="faq2_question">使用Outlook企业邮箱，为什么点击【提交】后没有响应？</span>
                                </h3>
                                <div class="text-gray-200 pl-8">
                                    <p class="mb-3">
                                        <span class="text-green-400" data-key="answer_label">答：</span><span data-key="faq2_answer_intro">Outlook官方会对部分账号增加一道验证步骤，需要用户完成验证后重新提交。验证步骤如下：</span>
                                    </p>
                                    <ol class="list-decimal list-inside space-y-2 ml-4">
                                        <li data-key="faq2_step1">点击【提交】后无响应时，登录Outlook官方邮箱</li>
                                        <li data-key="faq2_step2">找到验证邮件，点击【查看最近的活动】</li>
                                        <div class="mt-4 mb-4">
                                            <img src="assets/images/outlook1.png" alt="Outlook verification email screenshot" class="w-full max-w-2xl mx-auto rounded-lg shadow-lg border border-gray-600">
                                        </div>
                                        <li data-key="faq2_step3">点击【This was me】验证是本人操作</li>
                                        <div class="mt-4 mb-4">
                                            <img src="assets/images/outlook2.png" alt="Outlook verification action screenshot" class="w-full max-w-2xl mx-auto rounded-lg shadow-lg border border-gray-600">
                                        </div>
                                        <li data-key="faq2_step4">回到网红营销智能体的邮箱绑定界面，重新提交即可</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <script>
        // 翻译数据结构
        const translations = {
            zh: {
                page_title: '企业邮箱配置指南 - 网红营销智能体',
                nav_brand: '网红营销智能体',
                nav_trial_btn: '申请免费试用',
                main_title: '企业邮箱配置指南',
                overview_p1: '想通过企业邮箱来邀约网红，可以授权给第三方客户端（例如网红营销智能体平台）登录企业邮箱来发送邮件。',
                overview_p2: '授权只用于调用企业邮箱的发送邮件功能，本质上群发的邮件还是通过用户的企业邮箱发出，因此会受限于企业邮箱的发件规则。',
                warning_label: '注意：',
                warning_text: '目前网红营销智能体支持绑定大多数企业邮箱，但Outlook邮箱由于自身服务在支持SMTP时的稳定性问题，可能存在绑定失败的情况，因此我们建议优先选用其他企业邮件服务商。',
                section_auth_title: '一、授权方法',
                section_smtp_title: '二、SMTP配置帮助文档',
                section_troubleshoot_title: '三、邮件验证失败的常见原因及解决方法',
                section_faq_title: '四、常见问题',
                smtp_intro: '不同的企业邮箱如何开启、配置SMTP服务器以及获取授权码，方法可参考用户所使用的邮件服务商的帮助文档。以下整理了部分常用的企业邮箱的帮助文档，在高级选项默认配置下无法授权成功时，请按相关文档检查配置并验证。',
                table_provider: '邮箱服务商',
                table_smtp_server: 'SMTP服务器',
                table_auth_code: '需要授权码',
                table_config_doc: '配置文档',
                step1_title: '1. 进入邮箱配置',
                step1_desc: '点击个人头像 → 账户设置 → 进入邮件配置界面',
                step2_title: '2. 选择其他邮箱',
                step2_desc: '选择【其他邮箱】后输入以下信息：',
                email_address_info: '• 邮箱地址：填写企业邮箱地址',
                password_info: '• 密码：填写企业邮箱登录密码或客户端授权码',
                important_reminder_label: '重要提醒：',
                important_reminder_text: '网易企业邮箱、QQ邮箱、飞书企业邮箱、腾讯企业邮箱需要使用【授权码】登录。授权码是企业邮箱为第三方客户端登录而生成的专用密码，请在【密码】处填入授权码，而非邮箱登录密码。授权码获取方法详见下方【SMTP配置帮助文档】。',
                step3_title: '3. 高级选项配置',
                step3_desc: '请根据各自企业邮箱的需求，手动填写以下配置：',
                smtp_server_info: '• SMTP服务器：邮件发送服务器地址',
                imap_server_info: '• IMAP服务器：邮件接收服务器地址',
                protocol_info: '• 协议：选择对应的加密协议（SSL/TLS）',
                port_info: '• 端口：SMTP和IMAP对应的端口号',
                config_reference: '要求详细查看下方【SMTP配置帮助文档】，根据您使用的企业邮箱服务商查找对应的配置参数。',
                question_label: '问：',
                answer_label: '答：',
                faq1_question: '能否使用企业邮箱以外的个人邮箱发送？',
                faq1_answer: '如果不想使用企业邮箱发送，请参考其他个人邮箱（如qq、163等）对应邮箱配置。',
                faq2_question: '使用Outlook企业邮箱，为什么点击【提交】后没有响应？',
                faq2_answer_intro: 'Outlook官方会对部分账号增加一道验证步骤，需要用户完成验证后重新提交。验证步骤如下：',
                faq2_step1: '点击【提交】后无响应时，登录Outlook官方邮箱',
                faq2_step2: '找到验证邮件，点击【查看最近的活动】',
                faq2_step3: '点击【This was me】验证是本人操作',
                faq2_step4: '回到网红营销智能体的邮箱绑定界面，重新提交即可',
                troubleshoot_1_title: '1. 邮箱密码填写错误',
                troubleshoot_1_desc: '请检查邮箱账号和密码是否正确。部分企业邮箱需要【授权码】登录，授权码是企业邮箱为登录第三方客户端而引入的专用密码，不是所有企业邮箱都需要授权码登录，但有授权码时需要在【密码】处填入授权码，而非企业邮箱的登录密码，授权码获取方法详见上方【SMTP配置帮助文档】。',
                troubleshoot_2_title: '2. SMTP服务器配置错误',
                troubleshoot_2_desc: '与企业邮箱管理员确认使用的邮件服务商品牌，按该品牌的邮件服务帮助文档来配置，比如某个企业邮箱域名，但使用的其实是阿里企业邮箱服务，需要配置阿里企业邮箱的SMTP服务器。',
                troubleshoot_3_title: '3. 企业的SMTP服务未开启',
                troubleshoot_3_desc: '邮箱地址、密码、SMTP服务器均检查配置无误，依然无法绑定成功时，有可能是企业邮箱开启了禁止使用三方客户端登录，此时需要与邮箱管理员沟通关闭禁止功能，关闭后再次尝试绑定，开启方法详见上方【SMTP配置帮助文档】。',
                troubleshoot_4_title: '4. 用户个人邮箱账户开启了三方客户端安全密码',
                troubleshoot_4_desc: '在公司未禁止使用三方客户端登录的情况下绑定失败，可检查个人邮箱账户是否开启了三方客户端安全密码/授权码登录，如果开启了需要使用安全密码登录/授权码登录或者关闭三方客户端安全密码/授权码。',
                tip_label: '提示：',
                troubleshoot_tip: '为了验证是用户配置问题还是平台问题，可以尝试使用其他三方客户端用相同的配置信息绑定，比如使用Outlook客户端（Outlook客户端会自动给填写的邮箱解析出正确的SMTP配置信息）。',
                // 表格内容翻译
                yes: '是',
                no: '否',
                yes_no: '是/否',
                provider_tencent: '腾讯企业邮箱',
                provider_qq: 'QQ邮箱',
                provider_netease: '网易邮箱',
                provider_feishu: '飞书企业邮箱',
                provider_lark: 'Lark企业邮箱',
                provider_ali: '阿里企业邮箱',
                provider_outlook: 'Outlook企业邮箱',
                provider_sina: '新浪企业邮箱',
                provider_sohu: '搜狐企业邮箱',
                provider_zoho: 'ZOHO企业邮箱',
                provider_amazon: '亚马逊企业邮箱',
                provider_perfect: '完美邮箱',
                provider_dingtalk: '钉钉邮箱',
                provider_ionos: 'IONOS',
                provider_yunyou: '云邮',
                netease_desc: '(企业邮箱/126/163邮箱等)',
                outlook_unstable: '(不稳定)',
                tencent_doc1: '开启POP/SMTP/IMAP功能',
                tencent_doc2: '收发信返回错误码',
                qq_doc: 'QQ邮箱授权码配置',
                netease_smtp: '查询SMTP服务器',
                netease_doc1: '网易企业邮箱授权码',
                netease_doc2: '退信常见原因',
                feishu_doc1: '启用第三方客户端登录及生成专用密码',
                feishu_doc2: '邮箱常见退信报错和解决方法',
                lark_doc: 'Use Lark Mail on third-party email clients',
                ali_doc1: '开启SMTP服务',
                ali_doc2: '配置到第三方客户端',
                ali_doc3: '常见退信报错和解决方法',
                outlook_doc1: 'Outlook SMTP配置文档',
                outlook_doc2: 'Outlook SMTP设置界面（不要勾选ssl，并打开POP）',
                outlook_doc3: 'Outlook错误码',
                sina_doc: '客户端使用方法',
                sohu_doc: '开启SMTP服务',
                zoho_doc: '配置SMTP服务器',
                amazon_doc: '为亚马逊WorkMail设置IMAP',
                perfect_doc: '配置方法',
                dingtalk_doc: 'SMTP地址和端口信息',
                ionos_doc1: '配置方法',
                ionos_doc2: '错误码查询',
                yunyou_doc1: '常见企业邮箱POP/SMTP/IMAP服务器地址设置',
                yunyou_doc2: '开启/关闭客户端独立密码(授权码)'
            },
            en: {
                page_title: 'Enterprise Email Configuration Guide - Influencer Marketing AI Agent',
                nav_brand: 'Influencer Marketing AI Agent',
                nav_trial_btn: 'Apply for Free Trial',
                main_title: 'Enterprise Email Configuration Guide',
                overview_p1: 'To invite influencers through enterprise email, you can authorize third-party clients (such as the Influencer Marketing AI Agent platform) to log in to your enterprise email to send emails.',
                overview_p2: 'Authorization is only used to call the email sending function of enterprise email. Essentially, batch emails are still sent through the user\'s enterprise email, so they are subject to the sending rules of the enterprise email.',
                warning_label: 'Note:',
                warning_text: 'Currently, the Influencer Marketing AI Agent supports binding most enterprise emails, but Outlook emails may fail to bind due to stability issues when supporting SMTP. Therefore, we recommend prioritizing other enterprise email service providers.',
                section_auth_title: '1. Authorization Method',
                section_smtp_title: '2. SMTP Configuration Help Documentation',
                section_troubleshoot_title: '3. Common Causes and Solutions for Email Verification Failures',
                section_faq_title: '4. Frequently Asked Questions',
                smtp_intro: 'For different enterprise emails, how to enable and configure SMTP servers and obtain authorization codes, you can refer to the help documentation of the email service provider used by the user. The following organizes help documentation for some commonly used enterprise emails. When authorization fails under advanced option default configuration, please check the configuration and verify according to relevant documentation.',
                table_provider: 'Email Provider',
                table_smtp_server: 'SMTP Server',
                table_auth_code: 'Requires Auth Code',
                table_config_doc: 'Configuration Documentation',
                step1_title: '1. Enter Email Configuration',
                step1_desc: 'Click Personal Avatar → Account Settings → Enter Email Configuration Interface',
                step2_title: '2. Select Other Email',
                step2_desc: 'After selecting [Other Email], enter the following information:',
                email_address_info: '• Email Address: Fill in the enterprise email address',
                password_info: '• Password: Fill in the enterprise email login password or client authorization code',
                important_reminder_label: 'Important Reminder:',
                important_reminder_text: 'NetEase Enterprise Email, QQ Email, Feishu Enterprise Email, and Tencent Enterprise Email require login with [Authorization Code]. The authorization code is a dedicated password generated by the enterprise email for third-party client login. Please enter the authorization code in the [Password] field, not the email login password. For authorization code acquisition methods, see [SMTP Configuration Help Documentation] below.',
                step3_title: '3. Advanced Option Configuration',
                step3_desc: 'Please manually fill in the following configuration according to the needs of each enterprise email:',
                smtp_server_info: '• SMTP Server: Mail sending server address',
                imap_server_info: '• IMAP Server: Mail receiving server address',
                protocol_info: '• Protocol: Select the corresponding encryption protocol (SSL/TLS)',
                port_info: '• Port: SMTP and IMAP corresponding port numbers',
                config_reference: 'Please refer to [SMTP Configuration Help Documentation] below for details, and find the corresponding configuration parameters according to the enterprise email service provider you use.',
                question_label: 'Q:',
                answer_label: 'A:',
                faq1_question: 'Can I use personal email other than enterprise email for sending?',
                faq1_answer: 'If you don\'t want to use enterprise email for sending, please refer to the corresponding email configuration for other personal email accounts (such as QQ, 163, etc.).',
                faq2_question: 'When using Outlook enterprise email, why is there no response after clicking [Submit]?',
                faq2_answer_intro: 'Microsoft Outlook may add an additional verification step for some accounts, requiring users to complete verification before resubmitting. The verification steps are as follows:',
                faq2_step1: 'When there is no response after clicking [Submit], log in to the official Outlook mailbox',
                faq2_step2: 'Find the verification email and click [View Recent Activity]',
                faq2_step3: 'Click [This was me] to verify that it was your operation',
                faq2_step4: 'Return to the Influencer Marketing AI Agent\'s email binding interface and resubmit',
                troubleshoot_1_title: '1. Incorrect Email Password',
                troubleshoot_1_desc: 'Please check if the email account and password are correct. Some enterprise emails require [Authorization Code] login. The authorization code is a dedicated password introduced by enterprise email for third-party client login. Not all enterprise emails require authorization code login, but when an authorization code is available, you need to enter the authorization code in the [Password] field, not the enterprise email login password. For authorization code acquisition methods, see [SMTP Configuration Help Documentation] above.',
                troubleshoot_2_title: '2. SMTP Server Configuration Error',
                troubleshoot_2_desc: 'Confirm with the enterprise email administrator which email service provider brand is used, and configure according to the help documentation of that brand\'s email service. For example, a certain enterprise email domain may actually use Alibaba Enterprise Email service, which requires configuring the Alibaba Enterprise Email SMTP server.',
                troubleshoot_3_title: '3. Enterprise SMTP Service Not Enabled',
                troubleshoot_3_desc: 'When email address, password, and SMTP server configurations are all checked and correct, but binding still fails, it may be that the enterprise email has enabled prohibition of third-party client login. In this case, you need to communicate with the email administrator to disable the prohibition function, and try binding again after disabling it. For enabling methods, see [SMTP Configuration Help Documentation] above.',
                troubleshoot_4_title: '4. User Personal Email Account Has Third-party Client Security Password Enabled',
                troubleshoot_4_desc: 'When binding fails while the company has not prohibited third-party client login, you can check whether the personal email account has enabled third-party client security password/authorization code login. If enabled, you need to use security password login/authorization code login or disable third-party client security password/authorization code.',
                tip_label: 'Tip:',
                troubleshoot_tip: 'To verify whether it is a user configuration issue or a platform issue, you can try using other third-party clients to bind with the same configuration information, such as using the Outlook client (the Outlook client will automatically parse the correct SMTP configuration information for the filled email).',
                // 表格内容英文翻译
                yes: 'Yes',
                no: 'No',
                yes_no: 'Yes/No',
                provider_tencent: 'Tencent Enterprise Email',
                provider_qq: 'QQ Email',
                provider_netease: 'NetEase Email',
                provider_feishu: 'Feishu Enterprise Email',
                provider_lark: 'Lark Enterprise Email',
                provider_ali: 'Alibaba Enterprise Email',
                provider_outlook: 'Outlook Enterprise Email',
                provider_sina: 'Sina Enterprise Email',
                provider_sohu: 'Sohu Enterprise Email',
                provider_zoho: 'ZOHO Enterprise Email',
                provider_amazon: 'Amazon Enterprise Email',
                provider_perfect: 'Perfect Mail',
                provider_dingtalk: 'DingTalk Email',
                provider_ionos: 'IONOS',
                provider_yunyou: 'YunYou Email',
                netease_desc: '(Enterprise Email/126/163 Email, etc.)',
                outlook_unstable: '(Unstable)',
                tencent_doc1: 'Enable POP/SMTP/IMAP Functions',
                tencent_doc2: 'Send/Receive Error Codes',
                qq_doc: 'QQ Email Authorization Code Configuration',
                netease_smtp: 'Query SMTP Server',
                netease_doc1: 'NetEase Enterprise Email Authorization Code',
                netease_doc2: 'Common Bounce Reasons',
                feishu_doc1: 'Enable Third-party Client Login and Generate Dedicated Password',
                feishu_doc2: 'Common Email Bounce Errors and Solutions',
                lark_doc: 'Use Lark Mail on third-party email clients',
                ali_doc1: 'Enable SMTP Service',
                ali_doc2: 'Configure to Third-party Clients',
                ali_doc3: 'Common Bounce Errors and Solutions',
                outlook_doc1: 'Outlook SMTP Configuration Documentation',
                outlook_doc2: 'Outlook SMTP Settings Interface (Do not check SSL, and enable POP)',
                outlook_doc3: 'Outlook Error Codes',
                sina_doc: 'Client Usage Guide',
                sohu_doc: 'Enable SMTP Service',
                zoho_doc: 'Configure SMTP Server',
                amazon_doc: 'Set up IMAP for Amazon WorkMail',
                perfect_doc: 'Configuration Guide',
                dingtalk_doc: 'SMTP Address and Port Information',
                ionos_doc1: 'Configuration Guide',
                ionos_doc2: 'Error Code Lookup',
                yunyou_doc1: 'Common Enterprise Email POP/SMTP/IMAP Server Address Settings',
                yunyou_doc2: 'Enable/Disable Client Independent Password (Authorization Code)'
            }
        };

        // 检测系统语言并设置默认语言
        function detectSystemLanguage() {
            // 获取浏览器语言设置
            const browserLang = navigator.language || navigator.userLanguage;
            // 如果是中文相关语言，返回zh，否则返回en
            if (browserLang.startsWith('zh')) {
                return 'zh';
            } else {
                return 'en';
            }
        }

        // 当前语言 - 优先使用保存的偏好，否则根据系统语言自动选择
        let currentLanguage = localStorage.getItem('language') || detectSystemLanguage();

        // 切换语言函数
        function switchLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);

            // 更新页面标题
            document.title = translations[lang].page_title;
            
            // 更新所有带有data-key的元素
            document.querySelectorAll('[data-key]').forEach(element => {
                const key = element.getAttribute('data-key');
                if (translations[lang][key]) {
                    // 检查是否为input或textarea
                    if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                        element.placeholder = translations[lang][key];
                    } else {
                        element.textContent = translations[lang][key];
                    }
                }
            });

            // 更新当前语言显示
            const currentLangElement = document.getElementById('currentLang');
            if (currentLangElement) {
                currentLangElement.textContent = lang === 'zh' ? '中文' : 'English';
            }

            // 更新HTML lang属性
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
        }

        // 页面加载时初始化语言 - 根据系统语言智能选择
        document.addEventListener('DOMContentLoaded', function() {
            // 如果没有保存的语言偏好，根据系统语言自动设置
            if (!localStorage.getItem('language')) {
                currentLanguage = detectSystemLanguage();
                // 首次访问时不立即保存偏好，让用户有机会手动选择
                console.log(`检测到系统语言，自动设置为: ${currentLanguage === 'zh' ? '中文' : 'English'}`);
            }
            switchLanguage(currentLanguage);
        });

        // 滚动时导航栏效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(15, 15, 30, 0.98)';
                navbar.classList.add('scrolled');
            } else {
                navbar.style.background = 'rgba(15, 15, 30, 0.95)';
                navbar.classList.remove('scrolled');
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 语言切换功能
        const languageToggle = document.getElementById('languageToggle');
        const languageDropdown = document.getElementById('languageDropdown');

        if (languageToggle && languageDropdown) {
            languageToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', function() {
                languageDropdown.classList.add('hidden');
            });

            // 语言选择
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const lang = this.getAttribute('data-lang');
                    switchLanguage(lang);
                    languageDropdown.classList.add('hidden');
                });
            });
        }

        // 申请免费试用按钮点击事件
        function handleButtonClick() {
            const message = currentLanguage === 'zh' ? 
                '申请免费试用功能暂未开放，敬请期待！' : 
                'Free trial application feature is not yet available, please stay tuned!';
            alert(message);
        }
    </script>
</body>
</html>